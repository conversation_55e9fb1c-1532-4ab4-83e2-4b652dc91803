import chainlit as cl
import aiohttp
import json
import asyncio
from typing import Dict, Any, Optional, List
import os
import sys
from dotenv import load_dotenv
import logging
import base64
from io import BytesIO
from PIL import Image

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'backend'))

# Import RAG system components for direct integration
from query import QueryEngine
from prompts import AWSPromptSelector, validate_prompt_inputs, extract_aws_services
from advanced_retrieval import ConfigurableRetriever
from token_utils import get_token_tracker, TokenUsage

load_dotenv()

# Configuration
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8888")
TIMEOUT = aiohttp.ClientTimeout(total=300)  # 5 minutes timeout for ingestion

# Initialize logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global instances for direct RAG integration
query_engine = None
prompt_selector = None

class RAGClient:
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
    
    async def health_check(self) -> bool:
        """Check if the API is healthy"""
        try:
            async with aiohttp.ClientSession(timeout=TIMEOUT) as session:
                async with session.get(f"{self.base_url}/health") as response:
                    return response.status == 200
        except Exception:
            return False

    async def query_advanced(self, question: str, retrieval_config: Optional[Dict[str, Any]] = None, 
                           session_id: Optional[str] = None) -> Dict[str, Any]:
        """Send advanced query to the API with optional session management"""
        payload = {
            "question": question,
            "retrieval_config": retrieval_config or {}
        }
        
        # Add session_id as query parameter if provided
        url = f"{self.base_url}/query/advanced"
        if session_id:
            url += f"?session_id={session_id}"
        
        headers = {"Content-Type": "application/json"}

        async with aiohttp.ClientSession(timeout=TIMEOUT) as session:
            async with session.post(url, json=payload, headers=headers) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise Exception(f"Query failed: {error_text}")

    async def query_troubleshoot(self, question: str, retrieval_config: Optional[Dict[str, Any]] = None,
                               session_id: Optional[str] = None) -> Dict[str, Any]:
        """Send troubleshooting query to the MCP-enhanced API endpoint"""
        payload = {
            "question": question,
            "retrieval_config": retrieval_config or {}
        }

        # Add session_id as query parameter if provided
        url = f"{self.base_url}/query/troubleshoot"
        if session_id:
            url += f"?session_id={session_id}"

        headers = {"Content-Type": "application/json"}

        async with aiohttp.ClientSession(timeout=TIMEOUT) as session:
            async with session.post(url, json=payload, headers=headers) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise Exception(f"Troubleshooting query failed: {error_text}")

    async def query_aws_live(self, question: str, retrieval_config: Optional[Dict[str, Any]] = None,
                           session_id: Optional[str] = None) -> Dict[str, Any]:
        """Send query to the AWS Live endpoint for real-time AWS data"""
        payload = {
            "question": question,
            "retrieval_config": retrieval_config or {}
        }

        # Add session_id as query parameter if provided
        url = f"{self.base_url}/query/aws-live"
        if session_id:
            url += f"?session_id={session_id}"

        headers = {"Content-Type": "application/json"}

        async with aiohttp.ClientSession(timeout=TIMEOUT) as session:
            async with session.post(url, json=payload, headers=headers) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise Exception(f"AWS Live query failed: {error_text}")

    def is_troubleshooting_query(self, question: str) -> bool:
        """Detect if a query is troubleshooting-related (for legacy troubleshoot endpoint)"""
        troubleshooting_keywords = [
            "error", "issue", "problem", "fail", "slow", "down", "not working",
            "troubleshoot", "debug", "fix", "broken", "performance", "timeout",
            "high cpu", "memory", "latency", "500 error", "503 error", "502 error",
            "connection", "crash", "hang", "stuck", "degraded", "outage"
        ]

        question_lower = question.lower()
        return any(keyword in question_lower for keyword in troubleshooting_keywords)

    def is_aws_live_query(self, question: str) -> bool:
        """Detect if a query would benefit from AWS Live real-time data"""
        troubleshooting_keywords = [
            "error", "issue", "problem", "fail", "slow", "down", "not working",
            "troubleshoot", "debug", "fix", "broken", "performance", "timeout",
            "high cpu", "memory", "latency", "500 error", "503 error", "502 error",
            "connection", "crash", "hang", "stuck", "degraded", "outage"
        ]

        inventory_keywords = [
            "how many", "list all", "show me", "what instances", "what services",
            "current status", "running instances", "inventory", "count", "infrastructure",
            "do i have", "my instances", "my ec2", "my aws", "status of"
        ]

        cost_keywords = [
            "cost", "costs", "billing", "spend", "spending", "expensive", "price",
            "budget", "bill", "charges", "optimization", "optimize"
        ]

        aws_specific_keywords = [
            "ec2", "lambda", "s3", "rds", "cloudwatch", "aws", "instance", "bucket",
            "instances", "servers", "virtual machines", "vms"
        ]

        question_lower = question.lower()

        # More aggressive detection for EC2 instance queries
        if any(keyword in question_lower for keyword in ["how many", "do i have", "my instances", "my ec2"]):
            return True

        return (any(keyword in question_lower for keyword in troubleshooting_keywords) or
                any(keyword in question_lower for keyword in inventory_keywords) or
                any(keyword in question_lower for keyword in cost_keywords) or
                any(keyword in question_lower for keyword in aws_specific_keywords))

    async def query_diagram_graph(self, graph_data: Dict[str, Any], question: str) -> Dict[str, Any]:
        """Send diagram graph query to the API"""
        payload = {
            "graph_data": graph_data,
            "question": question
        }
        
        url = f"{self.base_url}/query/diagram-graph"
        headers = {"Content-Type": "application/json"}

        async with aiohttp.ClientSession(timeout=TIMEOUT) as session:
            async with session.post(url, json=payload, headers=headers) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise Exception(f"Diagram graph query failed: {error_text}")


    async def query_image_with_ocr(self, image_bytes: bytes, retrieval_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Upload an image to extract text via OCR and run retrieval queries.
        """
        url = f"{self.base_url}/query/image"
        data = aiohttp.FormData()
        data.add_field('file', image_bytes, filename='upload.png', content_type='image/png')
        
        if retrieval_config:
            data.add_field('retrieval_config', json.dumps(retrieval_config))
        
        async with aiohttp.ClientSession(timeout=TIMEOUT) as session:
            async with session.post(url, data=data) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise Exception(f"Image OCR query failed: {error_text}")



# Initialize client
rag_client = RAGClient(API_BASE_URL)

@cl.on_chat_start
async def start():
    """Initialize the chat session"""
    # Check API health
    is_healthy = await rag_client.health_check()
    
    if not is_healthy:
        await cl.Message(
            content="⚠️ **API Connection Failed**\n\nThe RAG API is not responding. Please check if the FastAPI server is running.",
            author="System"
        ).send()
        return
    
    # Initialize session state
    cl.user_session.set("api_healthy", True)
    # Default query mode: standard | aws-live | troubleshoot
    cl.user_session.set("query_mode", "standard")

    # Welcome message
    welcome_msg = """
# 🚀 Welcome to RAG Chat Assistant

I'm your intelligent document assistant powered by AWS Bedrock!

## 📚 **Available Commands:**
- **`/help`** - Show help message
- **`/mode [standard|aws-live|troubleshoot]`** - Switch query mode
- **`/diagrams`** - Show architecture diagrams from your documents
- **`/tokens`** - Show session token usage summary
- **`/ocr`** - Extract text from images and search documents
- **`/analyze`** - Analyze uploaded images with AI vision models

Ready to get started? Send a message to begin!
"""

    await cl.Message(content=welcome_msg, author="Assistant").send()

    # Initialize token tracking for this session
    session_id = cl.user_session.get("id", "default_session")
    token_tracker = get_token_tracker()
    session_usage = token_tracker.get_or_create_session(session_id)
    cl.user_session.set("session_id", session_id)
    cl.user_session.set("token_tracker", token_tracker)

@cl.on_message
async def main(message: cl.Message):
    """Handle incoming messages"""
    user_input = message.content.strip()
    
    # Check API health
    if not cl.user_session.get("api_healthy", False):
        await cl.Message(
            content="❌ API connection is not available. Please restart the chat.",
            author="System"
        ).send()
        return
    
    # Handle commands
    if user_input.startswith('/'):
        if user_input == "/help":
            help_msg = """
# 🆘 **Help & Commands**

## **Available Commands:**
- **`/help`** - Show this help message
- **`/diagrams`** - Show architecture diagrams from your documents
- **`/tokens`** - Show session token usage summary
- **`/ocr`** - Upload image for text extraction and document search
- **`/analyze`** - Analyze uploaded image with AI vision models

## **Query Examples:**

### **Regular Queries** (Standard RAG):
- "What are the best practices for AWS Lambda?"
- "Compare EC2 and ECS for container deployment"
- "How do I configure S3 bucket policies?"
- "Show me architecture diagrams for microservices"

### **AWS Live Queries** (Real-time AWS Infrastructure Data):
- "How many EC2 instances do I have and what's their status?"
- "What are my current AWS costs?"
- "Show me my AWS infrastructure inventory"
- "List all my running instances"
- "What AWS services am I using?"

### **Troubleshooting Queries** (Performance & Error Analysis):
- "My EC2 instance is running slowly, what should I check?"
- "I'm seeing 500 errors in my application logs"
- "High CPU usage on my instances, how do I troubleshoot?"
- "My Lambda functions are timing out"
- "Database connections are failing"
- "Application performance is degraded"

## **☁️ AWS Live Features:**
- **Real-time Infrastructure Data**: Live EC2, Lambda, S3, RDS inventory
- **Cost Analysis**: Current spending estimates and resource costs
- **Performance Metrics**: Live CloudWatch metrics and system status
- **Smart Detection**: Automatically detects when real-time data is needed
- **Combined Responses**: Documentation + live AWS data in one answer

## **🚨 Troubleshooting Features:**
- **Real-time AWS Monitoring**: Automatically fetches live CloudWatch metrics
- **Infrastructure Analysis**: Checks EC2, RDS, Lambda, and other AWS services
- **Enhanced Responses**: Combines documentation with current system status
- **Immediate Assessment**: Provides real-time situation analysis
- **Step-by-step Guidance**: Detailed troubleshooting procedures

## **Image Analysis Feature:**
- Upload architecture diagrams or screenshots with errors to get AI analysis
- Choose between AWS Bedrock (Claude) or Google Gemini for analysis
- The system will analyze the image content and provide detailed explanations
- For architecture diagrams: Components and relationships will be explained
- For error screenshots: The error will be identified with possible solutions

## **Advanced Query Features:**
- **Hybrid Search**: Combines semantic and keyword matching
- **Source Citations**: Shows which documents contain the information
- **Confidence Scores**: Indicates how relevant the results are
- **AWS Optimization**: Enhanced for AWS service documentation
- **Image Extraction**: Can extract and display architecture diagrams

Just type your question naturally - the system will automatically detect troubleshooting queries and use MCP enhancement!
            """
            await cl.Message(content=help_msg, author="Assistant").send()
        
        elif user_input.startswith("/mode"):
            parts = user_input.split()
            current_mode = cl.user_session.get("query_mode", "standard")
            if len(parts) == 1:
                await cl.Message(content=f"ℹ️ Current mode: `{current_mode}`\n\nUse `/mode [standard|aws-live|troubleshoot]` to switch.", author="Assistant").send()
            elif len(parts) == 2 and parts[1] in ["standard", "aws-live", "troubleshoot"]:
                cl.user_session.set("query_mode", parts[1])
                await cl.Message(content=f"✅ Mode set to: `{parts[1]}`", author="Assistant").send()
            else:
                await cl.Message(content="❌ Invalid mode. Use `/mode [standard|aws-live|troubleshoot]`.", author="Assistant").send()
            return

        elif user_input == "/diagrams":
            # Show typing indicator
            loading_msg = await cl.Message(
                content="🔍 Searching for architecture diagrams...",
                author="Assistant"
            ).send()
            
            try:
                # Query for architecture diagrams
                retrieval_config = {
                    "similarity_threshold": 0.2,  # Lower threshold to find more diagrams
                    "max_results": 10
                }
                
                # Create a query specifically for diagrams
                result = await rag_client.query_advanced(
                    "Find architecture diagrams and visualizations", 
                    retrieval_config
                )
                
                if result.get("images") and len(result["images"]) > 0:
                    loading_msg.content = f"✅ Found {len(result['images'])} architecture diagram(s) in your documents."
                    await loading_msg.update()
                    
                    # Display the diagrams
                    await display_images(result["images"])
                else:
                    loading_msg.content = "❌ No architecture diagrams found in your documents. Try using the standard query interface with terms like 'show me diagrams' or 'architecture'."
                    await loading_msg.update()
                    
            except Exception as e:
                loading_msg.content = f"❌ Error searching for diagrams: {str(e)}"
                await loading_msg.update()

        elif user_input == "/tokens":
            # Show session token usage summary
            session_usage = cl.user_session.get("session_usage")
            if session_usage and session_usage.total_queries > 0:
                await display_session_token_summary(session_usage)
            else:
                await cl.Message(
                    content="📊 **Token Usage**\n\nNo queries have been made in this session yet. Ask a question to start tracking token usage!",
                    author="Token Tracker"
                ).send()
                
        elif user_input == "/ocr":
            # Show image upload prompt for OCR + retrieval
            await cl.Message(
                content="📄 **OCR + Retrieval**\n\nUpload an image to extract text and search your documents.",
                author="Assistant"
            ).send()
            
            files = await cl.AskFileMessage(
                content="Upload an image file for text extraction",
                accept=["image/png", "image/jpeg", "image/jpg"],
                max_size_mb=10,
                timeout=180,
            ).send()
            
            if not files:
                await cl.Message(
                    content="❌ No file was uploaded or the upload timed out.",
                    author="Assistant"
                ).send()
                return
                
            file = files[0]
            loading_msg = await cl.Message(
                content="🔍 Extracting text and searching documents...",
                author="Assistant"
            ).send()
            
            try:
                # Show uploaded image
                temp_dir = os.path.join(os.getcwd(), "temp_images")
                os.makedirs(temp_dir, exist_ok=True)
                temp_file = os.path.join(temp_dir, f"ocr_{file.name}")
                
                # Get file content - try multiple approaches for Chainlit compatibility
                file_content = None
                with open(temp_file, "wb") as f:
                    if hasattr(file, 'content') and file.content is not None:
                        file_content = file.content
                        f.write(file_content)
                    elif hasattr(file, 'path') and file.path and os.path.exists(file.path):
                        with open(file.path, 'rb') as src:
                            file_content = src.read()
                            f.write(file_content)
                    elif hasattr(file, 'file') and file.file is not None:
                        # Some versions use 'file' attribute
                        file_content = file.file.read()
                        f.write(file_content)
                    else:
                        # Last resort - try to read as bytes directly
                        try:
                            file_content = bytes(file)
                            f.write(file_content)
                        except:
                            raise Exception(f"Cannot access file content. File type: {type(file)}, Available attributes: {[attr for attr in dir(file) if not attr.startswith('_')]}")

                image_element = cl.Image(path=temp_file, name="ocr_image")
                await cl.Message(
                    content=f"**Processing Image**: {file.name}",
                    elements=[image_element]
                ).send()

                # Process with OCR + retrieval
                result = await rag_client.query_image_with_ocr(file_content)
                response_content = format_query_response(result)
                
                # Add extracted text section
                extracted_text = result.get("extracted_text", "").strip()
                if extracted_text:
                    response_content += f"\n\n## 📝 **Extracted Text**\n\n```\n{extracted_text[:500]}\n```"
                    if len(extracted_text) > 500:
                        response_content += "\n*(text truncated)*"
                
                loading_msg.content = response_content
                await loading_msg.update()
                
                # Show sources if available
                if result.get("sources"):
                    sources_msg = format_sources(result["sources"])
                    await cl.Message(content=sources_msg, author="Sources").send()
                    
            except Exception as e:
                loading_msg.content = f"❌ **OCR Query Failed**\n\nError: {str(e)}"
                await loading_msg.update()
                
        elif user_input == "/analyze":
            # Show image upload prompt
            await cl.Message(
                content="📸 **Image Analysis**\n\nPlease upload an architecture diagram or error screenshot for AI analysis.",
                author="Assistant"
            ).send()
            
            # Request file upload from user
            files = await cl.AskFileMessage(
                content="Upload an image file (PNG, JPG, or SVG)",
                accept=["image/png", "image/jpeg", "image/jpg", "image/svg+xml"],
                max_size_mb=10,
                timeout=180,
            ).send()
            
            if not files:
                await cl.Message(
                    content="❌ No file was uploaded or the upload timed out.",
                    author="Assistant"
                ).send()
                return
                
            # Get the uploaded file
            file = files[0]
            
            # Show loading message
            loading_msg = await cl.Message(
                content="🔍 Analyzing your image...",
                author="Assistant"
            ).send()
            
            try:
                # Create a temporary file to display the uploaded image
                temp_dir = os.path.join(os.getcwd(), "temp_images")
                os.makedirs(temp_dir, exist_ok=True)
                temp_file = os.path.join(temp_dir, f"uploaded_{file.name}")
                
                # Save the uploaded image - try multiple approaches for Chainlit compatibility
                file_content = None
                with open(temp_file, "wb") as f:
                    if hasattr(file, 'content') and file.content is not None:
                        file_content = file.content
                        f.write(file_content)
                    elif hasattr(file, 'path') and file.path and os.path.exists(file.path):
                        with open(file.path, 'rb') as src:
                            file_content = src.read()
                            f.write(file_content)
                    elif hasattr(file, 'file') and file.file is not None:
                        file_content = file.file.read()
                        f.write(file_content)
                    else:
                        try:
                            file_content = bytes(file)
                            f.write(file_content)
                        except:
                            raise Exception(f"Cannot access file content. File type: {type(file)}, Available attributes: {[attr for attr in dir(file) if not attr.startswith('_')]}")
                
                # Display the uploaded image
                image_element = cl.Image(path=temp_file, name="uploaded_image")
                await cl.Message(
                    content=f"**Uploaded Image**: {file.name}",
                    elements=[image_element]
                ).send()
                
                # Ask user which analysis method to use - simplified approach
                choice_msg = await cl.AskUserMessage(
                    content="Choose an analysis method:\n1. AWS Bedrock (Claude) - type 'aws'\n2. Google Gemini 2.0 Flash - type 'gemini'",
                    timeout=60,
                ).send()

                if not choice_msg:
                    loading_msg.content = "❌ No analysis method selected or the selection timed out."
                    await loading_msg.update()
                    return

                analysis_choice_value = choice_msg.get('output', '').lower().strip()

                # Debug: Show what choice was made
                await cl.Message(content=f"🔧 DEBUG: User choice = '{analysis_choice_value}'", author="Debug").send()

                if analysis_choice_value not in ['aws', 'gemini', 'openrouter']:  # Keep openrouter for backward compatibility
                    loading_msg.content = "❌ Invalid choice. Please type 'aws' or 'gemini'."
                    await loading_msg.update()
                    return


                
                # Get file content for analysis (if not already obtained above)
                if file_content is None:
                    if hasattr(file, 'content') and file.content is not None:
                        file_content = file.content
                    elif hasattr(file, 'path') and file.path and os.path.exists(file.path):
                        with open(file.path, 'rb') as f:
                            file_content = f.read()
                    elif hasattr(file, 'file') and file.file is not None:
                        file_content = file.file.read()
                    else:
                        try:
                            file_content = bytes(file)
                        except:
                            raise Exception(f"Cannot access file content. File type: {type(file)}, Available attributes: {[attr for attr in dir(file) if not attr.startswith('_')]}")

                # Analyze the image with the selected method
                use_gemini = analysis_choice_value in ["gemini", "openrouter"]  # Support both for backward compatibility

                analysis_result = await analyze_image_with_ai(file_content, use_gemini)

                # Format and display the analysis result
                if analysis_result.get("status") == "error":
                    error_msg = analysis_result.get("error", "Unknown error")
                    loading_msg.content = f"❌ **Analysis Failed**\n\n{error_msg}"
                    await loading_msg.update()
                else:
                    # Extract text content if available
                    extracted_text = analysis_result.get("extracted_text", "").strip()
                    extracted_text_section = ""
                    if extracted_text:
                        extracted_text_section = f"""
## 📝 **Extracted Text**

```
{extracted_text[:500]}
```
{'' if len(extracted_text) <= 500 else '*(text truncated)*'}

"""
                    
                    # Format the analysis
                    analysis = analysis_result.get("analysis", "No analysis available")
                    model_id = analysis_result.get("model_id", "Unknown model")

                    # Debug: Log the analysis validation
                    analysis_debug = f"Analysis check: type={type(analysis)}, len={len(analysis) if analysis else 0}, bool={bool(analysis)}"
                    print(f"FRONTEND DEBUG: {analysis_debug}")

                    # Ensure analysis is a string and not empty
                    if not analysis or not isinstance(analysis, str):
                        print(f"FRONTEND DEBUG: Analysis failed validation, using fallback")
                        analysis = "No analysis content received from the AI model."
                    else:
                        print(f"FRONTEND DEBUG: Analysis passed validation")

                    # Clean the analysis content (only normalize line endings)
                    analysis_clean = analysis.replace('\r\n', '\n').replace('\r', '\n')

                    result_content = f"""# 🔍 Image Analysis Results

## 🤖 AI Analysis

{analysis_clean}

{extracted_text_section}---
*Analysis performed by: {model_id}*"""

                    loading_msg.content = result_content
                    await loading_msg.update()

                    # If the analysis contains a graph, add a button to ask questions about it
                    if "graph" in analysis_result:
                        await cl.Message(
                            content="💡 **Tip**: You can ask questions about this diagram using the regular chat!",
                            author="Assistant"
                        ).send()
            
            except Exception as e:
                loading_msg.content = f"❌ **Analysis Failed**\n\nError: {str(e)}"
                await loading_msg.update()

        else:
            await cl.Message(
                content="❓ Unknown command. Use `/help` to see available commands.",
                author="Assistant"
            ).send()
        return
    
    # Check for diagram requests
    is_diagram_request = any(term in user_input.lower() for term in [
        "diagram", "architecture", "visual", "picture", "image", "show me"
    ])

    # Check for troubleshooting requests
    is_troubleshooting_request = rag_client.is_troubleshooting_query(user_input)

    # Check for AWS Live requests (inventory, cost, AWS-specific queries)
    is_aws_live_request = rag_client.is_aws_live_query(user_input)

    # Read user-selected mode
    mode = cl.user_session.get("query_mode", "standard")

    # Handle queries with appropriate endpoint
    await handle_query(user_input, is_diagram_request, is_troubleshooting_request, is_aws_live_request, mode)



async def handle_query(question: str, is_diagram_request: bool = False, is_troubleshooting_request: bool = False, is_aws_live_request: bool = False, mode: str = "standard"):
    """Handle user queries with appropriate endpoint"""

    # If user explicitly selected a mode, override detections
    if mode == "aws-live":
        is_aws_live_request = True
        is_troubleshooting_request = False
    elif mode == "troubleshoot":
        is_troubleshooting_request = True
        is_aws_live_request = False

    # Show appropriate typing indicator
    if is_aws_live_request:
        loading_msg = await cl.Message(
            content="☁️ Fetching real-time AWS data and searching documentation...",
            author="Assistant"
        ).send()
    elif is_troubleshooting_request:
        loading_msg = await cl.Message(
            content="🔍 Analyzing your AWS infrastructure and searching for troubleshooting guidance...",
            author="Assistant"
        ).send()
    else:
        loading_msg = await cl.Message(
            content="🤔 Searching through your documents...",
            author="Assistant"
        ).send()

    try:
        # Default retrieval configuration
        retrieval_config = {
            "similarity_threshold": 0.3,
            "max_results": 8,
            "use_hybrid": True,
            "hybrid_weights": [0.7, 0.3],
            "aws_boost": True,
            "filter_duplicates": True
        }

        # Choose appropriate endpoint based on query type
        if is_aws_live_request:
            # Use AWS Live endpoint for real-time AWS data
            result = await rag_client.query_aws_live(question, retrieval_config)
        elif is_troubleshooting_request:
            # Use MCP-enhanced troubleshooting endpoint
            result = await rag_client.query_troubleshoot(question, retrieval_config)
        else:
            # Use regular advanced query endpoint
            result = await rag_client.query_advanced(question, retrieval_config)

        # Track token usage for this session
        token_usage_data = result.get("token_usage")
        if token_usage_data:
            session_id = cl.user_session.get("session_id", "default_session")
            token_tracker = cl.user_session.get("token_tracker")
            if token_tracker:
                # Create TokenUsage object from the response data
                token_usage = TokenUsage(
                    input_tokens=token_usage_data.get("input_tokens", 0),
                    output_tokens=token_usage_data.get("output_tokens", 0),
                    total_tokens=token_usage_data.get("total_tokens", 0),
                    model_name=token_usage_data.get("model_name"),
                    timestamp=token_usage_data.get("timestamp")
                )
                # Track the usage in the session
                session_usage = token_tracker.track_query_usage(session_id, token_usage)
                # Store updated session usage
                cl.user_session.set("session_usage", session_usage)

        # Format the response
        response_content = format_query_response(result)

        # Update the loading message with the response
        loading_msg.content = response_content
        await loading_msg.update()

        # Display session token summary if available
        session_usage = cl.user_session.get("session_usage")
        if session_usage and session_usage.total_queries > 1:
            await display_session_token_summary(session_usage)

        # Display images if available
        if result.get("images") and len(result["images"]) > 0:
            await display_images(result["images"])
        # Also check for images in sources and display them
        elif result.get("sources"):
            image_sources = [source for source in result.get("sources", []) 
                           if source.get("source", "").endswith((".png", ".jpg", ".jpeg", ".gif")) 
                           or "_img_" in source.get("source", "")]
            if image_sources:
                # Request images specifically for these sources
                try:
                    image_query = "Show me images related to " + question
                    image_result = await rag_client.query_advanced(image_query, {"retriever_type": "configurable", "max_results": 10})
                    if image_result.get("images") and len(image_result["images"]) > 0:
                        await cl.Message(content="📊 **Related Images Found**").send()
                        await display_images(image_result["images"])
                except Exception as e:
                    logger.error(f"Error fetching related images: {e}")
                    await cl.Message(content="📊 **Related Images Found in Sources** (Unable to display)").send()

        # Show sources as a separate message if available
        if result.get("sources"):
            sources_msg = format_sources(result["sources"])
            await cl.Message(content=sources_msg, author="Sources").send()
    
    except Exception as e:
        error_msg = f"""
❌ **Query Failed**

**Error**: {str(e)}

**Suggestions:**
- Try rephrasing your question
- Check if the API server is running
        """
        loading_msg.content = error_msg
        await loading_msg.update()

async def display_session_token_summary(session_usage: TokenUsage) -> None:
    """Display cumulative token usage summary for the session."""
    try:
        total_queries = session_usage.total_queries
        cumulative_input = session_usage.cumulative_input_tokens
        cumulative_output = session_usage.cumulative_output_tokens
        cumulative_total = session_usage.cumulative_total_tokens

        averages = session_usage.get_average_tokens_per_query()

        summary_msg = f"""
📊 **Session Token Summary**

**Total Queries**: {total_queries}
**Cumulative Usage**: {cumulative_input:,} input + {cumulative_output:,} output = {cumulative_total:,} total tokens

**Average per Query**: {averages['input']:.1f} input + {averages['output']:.1f} output = {averages['total']:.1f} total tokens
        """

        await cl.Message(content=summary_msg.strip(), author="Token Tracker").send()

    except Exception as e:
        logger.error(f"Error displaying session token summary: {e}")

# Add a new function to display images
async def display_images(images: List[Dict[str, Any]]):
    """Display images in the UI"""
    if not images:
        return
    
    for i, img_data in enumerate(images):
        try:
            # Get image details
            base64_str = img_data.get("base64")
            if not base64_str:
                continue
                
            caption = img_data.get("caption", "Architecture Diagram")
            source_doc = img_data.get("source_document", "Unknown source")
            
            # Decode base64 image
            image_bytes = base64.b64decode(base64_str)
            
            # Create a temporary file to store the image
            temp_dir = os.path.join(os.getcwd(), "temp_images")
            os.makedirs(temp_dir, exist_ok=True)
            temp_file = os.path.join(temp_dir, f"diagram_{i}.png")
            
            # Save the image to the temporary file
            with open(temp_file, "wb") as f:
                f.write(image_bytes)
            
            # Create image element
            image_element = cl.Image(path=temp_file, name=f"diagram_{i}")
            
            # Create and send message with the image element attached
            await cl.Message(
                content=f"**{caption}**\n*Source: {source_doc}*",
                elements=[image_element]
            ).send()
            
        except Exception as e:
            logger.error(f"Error displaying image: {str(e)}")
            await cl.Message(
                content=f"⚠️ Failed to display an image. Error: {str(e)}",
                author="System"
            ).send()

async def upload_image_and_query(image_bytes: bytes, retrieval_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Upload an image to the backend, extract text, and run retrieval.
    """
    url = f"{API_BASE_URL}/query/image"
    data = aiohttp.FormData()
    data.add_field('file', image_bytes, filename='upload.png', content_type='image/png')
    
    payload = {}
    if retrieval_config:
        payload['retrieval_config'] = retrieval_config
    data.add_field('request', json.dumps(payload), content_type='application/json')
    async with aiohttp.ClientSession(timeout=TIMEOUT) as session:
        async with session.post(url, data=data) as response:
            if response.status == 200:
                return await response.json()
            else:
                error_text = await response.text()
                raise Exception(f"Image query failed: {error_text}")

async def analyze_image_with_ai(image_bytes: bytes, use_gemini: bool = False, custom_prompt: Optional[str] = None) -> Dict[str, Any]:
    """
    Upload an image to the backend for AI analysis using vision model.
    This is specifically for analyzing architecture diagrams or screenshots with errors.

    Args:
        image_bytes: The image bytes to analyze
        use_gemini: Whether to use Google Gemini API instead of AWS Bedrock
    """
    import requests

    # Choose the appropriate endpoint based on the use_gemini flag
    if use_gemini:
        url = f"{API_BASE_URL}/analyze/image/gemini"
    else:
        url = f"{API_BASE_URL}/analyze/image"

    # Use requests instead of aiohttp to avoid chainlit async issues
    files = {'file': ('upload.png', image_bytes, 'image/png')}
    data = {}

    # Add custom prompt for Gemini
    if use_gemini and custom_prompt:
        data['prompt'] = custom_prompt

    try:
        response = requests.post(url, files=files, data=data, timeout=120)

        if response.status_code == 200:
            result = response.json()
            return result
        else:
            error_text = response.text
            raise Exception(f"Image analysis failed: {error_text}")

    except Exception as e:
        raise



async def display_token_usage_from_dict(token_usage: Dict[str, Any], activity: str):
    """Display token usage from a dictionary."""
    input_tokens = token_usage.get("input_tokens", 0)
    output_tokens = token_usage.get("output_tokens", 0)
    total_tokens = token_usage.get("total_tokens", 0)

    summary_msg = f"""
    🔢 **Token Usage ({activity})**

    **Input**: {input_tokens:,} | **Output**: {output_tokens:,} | **Total**: {total_tokens:,}
    """
    await cl.Message(content=summary_msg.strip(), author="Token Tracker").send()

def format_query_response(result: Dict[str, Any]) -> str:
    """Format the query response for display"""
    answer = result.get("answer", "No answer provided")
    query_type = result.get("query_type", "unknown")
    confidence = result.get("confidence")

    # Check for MCP enhancement
    mcp_enhanced = result.get("enhanced_with_mcp", False)
    aws_real_time_data = result.get("aws_real_time_data", False)
    troubleshooting_mode = result.get("troubleshooting_mode", False)

    # Add appropriate header based on enhancement type
    if mcp_enhanced and aws_real_time_data:
        response = "## ☁️ **AWS Live Response**\n\n"
        response += "🔴 *This response includes real-time AWS infrastructure data*\n\n"
    elif mcp_enhanced:
        response = "## 🚨 **MCP-Enhanced Troubleshooting Response**\n\n"
        response += "☁️ *This response includes real-time AWS infrastructure data*\n\n"
    else:
        response = "## 💡 **Answer**\n\n"

    response += f"{answer}\n\n"

    # Add metadata
    metadata_parts = []
    metadata_parts.append(f"**Query Type**: {query_type}")

    # Add MCP-specific metadata
    if mcp_enhanced:
        metadata_parts.append("**MCP Enhanced**: ✅ YES")
    if aws_real_time_data:
        metadata_parts.append("**Real-time AWS Data**: ✅ YES")
    if troubleshooting_mode:
        metadata_parts.append("**Troubleshooting Mode**: ✅ YES")

    if confidence:
        metadata_parts.append(f"**Confidence**: {confidence}")

    if result.get("has_aws_content"):
        metadata_parts.append("**AWS Content**: ✅ Detected")

    if result.get("has_images"):
        metadata_parts.append("**Images**: ✅ Available")

    if result.get("key_findings"):
        findings = result["key_findings"]
        if isinstance(findings, list) and findings:
            metadata_parts.append(f"**Key Findings**: {len(findings)} items")

    if metadata_parts:
        response += "📋 **Query Details**\n" + " | ".join(metadata_parts) + "\n\n"

    # Add token usage information
    token_usage = result.get("token_usage")
    if token_usage:
        input_tokens = token_usage.get("input_tokens", 0)
        output_tokens = token_usage.get("output_tokens", 0)
        total_tokens = token_usage.get("total_tokens", 0)
        model_name = token_usage.get("model_name", "Unknown")

        response += "🔢 **Token Usage**\n"
        response += f"**Input**: {input_tokens:,} tokens | **Output**: {output_tokens:,} tokens | **Total**: {total_tokens:,} tokens\n"
        response += f"*Model*: {model_name}\n\n"

    return response

def format_sources(sources: list) -> str:
    """Format sources for display"""
    if not sources:
        return "📚 **Sources**: No sources available"
    
    sources_text = "📚 **Sources & References**\n\n"
    
    for i, source in enumerate(sources[:5], 1):  # Limit to top 5 sources
        source_name = source.get("source", "Unknown")
        score = source.get("score", 0)
        preview = source.get("content_preview", "No preview available")
        
        sources_text += f"**{i}. {source_name}**\n"
        sources_text += f"*Relevance: {score:.2f}*\n"
        sources_text += f"```\n{preview}\n```\n\n"
    
    if len(sources) > 5:
        sources_text += f"*... and {len(sources) - 5} more sources*\n"
    
    return sources_text

# No need for a __main__ block as chainlit is run via the CLI
# For direct running, you would use: chainlit run chainlit_app.py

@cl.action_callback("ask_graph")
async def on_ask_graph(action: cl.Action):
    """Handle the 'ask_graph' action."""
    graph_data = json.loads(action.value)
    
    # Ask the user for a question about the diagram
    res = await cl.AskUserMessage(content="What would you like to know about the diagram's structure? (e.g., 'What is connected to the database?')", timeout=60).send()
    
    if res:
        question = res['output']
        
        # Show loading message
        loading_msg = await cl.Message(
            content="Analyzing the diagram's structure...",
            author="Assistant"
        ).send()
        
        try:
            # This is a simplified example. In a real application, you would have a dedicated endpoint for this.
            # For now, we'll call a hypothetical endpoint.
            # In a real implementation, you would call the `query_diagram_graph` method on the backend.
            # We will simulate this by calling a local function.
            
            # Simulate calling the backend
            result = await rag_client.query_diagram_graph(graph_data, question)
            answer = result.get("answer", "No answer provided.")
            
            loading_msg.content = f"## 📊 Diagram Analysis\n\n{answer}"
            await loading_msg.update()
            
        except Exception as e:
            loading_msg.content = f"❌ Error analyzing diagram structure: {str(e)}"
            await loading_msg.update()
