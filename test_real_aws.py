#!/usr/bin/env python3
"""
Test MCP integration with real AWS infrastructure
Discovers your actual AWS resources and tests real-time monitoring
"""

import boto3
import requests
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any

# Load AWS credentials from environment
import os
from dotenv import load_dotenv
load_dotenv()

def discover_aws_resources():
    """Discover actual AWS resources in your account"""
    print("🔍 Discovering your AWS resources...")
    
    try:
        # Initialize AWS clients
        session = boto3.Session(
            aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
            aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
            region_name=os.getenv("AWS_REGION", "ap-south-1")
        )
        
        resources = {}
        
        # EC2 Instances
        try:
            ec2 = session.client('ec2')
            instances = ec2.describe_instances()
            ec2_instances = []
            for reservation in instances['Reservations']:
                for instance in reservation['Instances']:
                    if instance['State']['Name'] != 'terminated':
                        ec2_instances.append({
                            'InstanceId': instance['InstanceId'],
                            'InstanceType': instance['InstanceType'],
                            'State': instance['State']['Name'],
                            'LaunchTime': instance.get('LaunchTime', 'Unknown')
                        })
            resources['EC2'] = ec2_instances
            print(f"✅ Found {len(ec2_instances)} EC2 instances")
        except Exception as e:
            print(f"⚠️  EC2 discovery failed: {e}")
            resources['EC2'] = []
        
        # RDS Instances
        try:
            rds = session.client('rds')
            db_instances = rds.describe_db_instances()
            rds_instances = []
            for db in db_instances['DBInstances']:
                rds_instances.append({
                    'DBInstanceIdentifier': db['DBInstanceIdentifier'],
                    'DBInstanceClass': db['DBInstanceClass'],
                    'DBInstanceStatus': db['DBInstanceStatus'],
                    'Engine': db['Engine']
                })
            resources['RDS'] = rds_instances
            print(f"✅ Found {len(rds_instances)} RDS instances")
        except Exception as e:
            print(f"⚠️  RDS discovery failed: {e}")
            resources['RDS'] = []
        
        # Lambda Functions
        try:
            lambda_client = session.client('lambda')
            functions = lambda_client.list_functions()
            lambda_functions = []
            for func in functions['Functions']:
                lambda_functions.append({
                    'FunctionName': func['FunctionName'],
                    'Runtime': func['Runtime'],
                    'LastModified': func['LastModified']
                })
            resources['Lambda'] = lambda_functions
            print(f"✅ Found {len(lambda_functions)} Lambda functions")
        except Exception as e:
            print(f"⚠️  Lambda discovery failed: {e}")
            resources['Lambda'] = []
        
        # S3 Buckets
        try:
            s3 = session.client('s3')
            buckets = s3.list_buckets()
            s3_buckets = [bucket['Name'] for bucket in buckets['Buckets']]
            resources['S3'] = s3_buckets
            print(f"✅ Found {len(s3_buckets)} S3 buckets")
        except Exception as e:
            print(f"⚠️  S3 discovery failed: {e}")
            resources['S3'] = []
        
        return resources
        
    except Exception as e:
        print(f"❌ AWS discovery failed: {e}")
        return {}

def test_real_time_monitoring(resources: Dict[str, List]):
    """Test real-time monitoring with actual resources"""
    print(f"\n{'='*60}")
    print("🔴 TESTING REAL-TIME AWS MONITORING")
    print(f"{'='*60}")
    
    # Generate specific troubleshooting queries based on discovered resources
    test_queries = []
    
    # EC2-specific queries
    if resources.get('EC2'):
        instance_id = resources['EC2'][0]['InstanceId']
        test_queries.extend([
            f"My EC2 instance {instance_id} is running slowly, what should I check?",
            f"High CPU usage on EC2 instance {instance_id}, how do I troubleshoot this?",
            "My EC2 instances are experiencing performance issues, what metrics should I monitor?"
        ])
    
    # RDS-specific queries
    if resources.get('RDS'):
        db_id = resources['RDS'][0]['DBInstanceIdentifier']
        test_queries.extend([
            f"Database {db_id} connections are timing out, what could be wrong?",
            f"RDS instance {db_id} is slow, how do I diagnose the issue?",
            "My database performance is degraded, what should I check?"
        ])
    
    # Lambda-specific queries
    if resources.get('Lambda'):
        func_name = resources['Lambda'][0]['FunctionName']
        test_queries.extend([
            f"Lambda function {func_name} is throwing errors, how do I troubleshoot?",
            f"My Lambda function {func_name} has high latency, what should I check?",
            "Lambda functions are failing, how do I debug this?"
        ])
    
    # General queries if no specific resources
    if not any(resources.values()):
        test_queries = [
            "My AWS infrastructure is experiencing performance issues, what should I monitor?",
            "I'm seeing high costs in my AWS bill, how do I identify the cause?",
            "My application is slow, what AWS metrics should I check?"
        ]
    
    # Test each query
    for i, query in enumerate(test_queries[:3], 1):  # Test first 3 queries
        print(f"\n🧪 Test {i}: Real-time troubleshooting")
        print(f"Query: {query}")
        print("-" * 50)
        
        result = test_mcp_query(query)
        
        if result and not result.get('error'):
            mcp_enhanced = result.get('enhanced_with_mcp', False)
            aws_data = result.get('aws_real_time_data', False)
            
            print(f"✅ MCP Enhanced: {mcp_enhanced}")
            print(f"☁️  Real-time AWS Data: {aws_data}")
            
            # Show first part of answer
            answer = result.get('answer', '')
            if answer:
                print(f"📝 Response preview: {answer[:200]}...")
        else:
            print(f"❌ Query failed: {result.get('error', 'Unknown error')}")
        
        time.sleep(3)  # Wait between tests

def test_mcp_query(question: str) -> Dict[str, Any]:
    """Send a query to the MCP-enhanced endpoint"""
    try:
        response = requests.post(
            "http://localhost:8888/query/advanced",
            json={
                "question": question,
                "retrieval_config": {
                    "retriever_type": "multi_vector",
                    "use_compression": True,
                    "similarity_threshold": 0.3
                }
            },
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": f"HTTP {response.status_code}"}
            
    except Exception as e:
        return {"error": str(e)}

def main():
    """Main test function"""
    print("🚀 Real-time AWS MCP Integration Test")
    print("=" * 60)
    
    # Discover your actual AWS resources
    resources = discover_aws_resources()
    
    if not any(resources.values()):
        print("\n⚠️  No AWS resources discovered. Testing with general queries...")
    else:
        print(f"\n📊 Resource Summary:")
        for service, items in resources.items():
            if items:
                print(f"  • {service}: {len(items)} resources")
    
    # Test real-time monitoring
    test_real_time_monitoring(resources)
    
    print(f"\n{'='*60}")
    print("🎯 Real-time MCP Test Complete!")
    print("=" * 60)
    print("✅ Your MCP integration can now monitor your actual AWS infrastructure")
    print("🔗 Backend: http://localhost:8888")

if __name__ == "__main__":
    main()
