#!/usr/bin/env python3
"""
Test the current backend on port 8888
"""

import requests
import json

def test_backend():
    """Test the backend endpoints"""
    
    base_url = "http://localhost:8888"
    
    print("🔍 Testing Backend Endpoints")
    print("=" * 50)
    
    # Test health endpoint
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        if response.status_code == 200:
            print("✅ Health endpoint: OK")
        else:
            print(f"❌ Health endpoint: HTTP {response.status_code}")
            return
    except Exception as e:
        print(f"❌ Health endpoint: {e}")
        return
    
    # Test AWS Live endpoint
    print("\n🔍 Testing AWS Live endpoint...")
    try:
        payload = {
            "question": "How many EC2 instances do I have?",
            "retrieval_config": {
                "retriever_type": "multi_vector",
                "use_compression": True,
                "similarity_threshold": 0.3
            }
        }
        
        response = requests.post(
            f"{base_url}/query/aws-live",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ AWS Live endpoint: OK")
            print(f"🔧 MCP Enhanced: {result.get('enhanced_with_mcp', False)}")
            print(f"☁️  AWS Real-time Data: {result.get('aws_real_time_data', False)}")
            
            answer = result.get('answer', '')
            if "Real-time AWS Infrastructure Data" in answer:
                print("🔍 ✅ Contains real-time AWS data section")
            if "EC2 Instances:" in answer:
                print("📊 ✅ Contains EC2 inventory")
                
        elif response.status_code == 404:
            print("❌ AWS Live endpoint: 404 Not Found")
            print("   The /query/aws-live endpoint is not available")
        else:
            print(f"❌ AWS Live endpoint: HTTP {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ AWS Live endpoint: {e}")
    
    # Test standard endpoint
    print("\n🔍 Testing standard endpoint...")
    try:
        response = requests.post(
            f"{base_url}/query/advanced",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        if response.status_code == 200:
            print("✅ Standard endpoint: OK")
        else:
            print(f"❌ Standard endpoint: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ Standard endpoint: {e}")

if __name__ == "__main__":
    test_backend()
