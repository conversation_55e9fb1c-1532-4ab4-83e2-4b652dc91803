#!/usr/bin/env python3
"""
Test enhanced inventory and cost queries with updated MCP integration
"""

import requests
import json
import time

def test_enhanced_queries():
    """Test the enhanced inventory and cost queries"""
    
    backend_url = "http://localhost:8889"  # Updated port
    
    test_queries = [
        {
            "question": "How many EC2 instances do I have and what are their current status?",
            "category": "Inventory",
            "should_use_mcp": True
        },
        {
            "question": "List all my AWS services and their current status",
            "category": "Inventory", 
            "should_use_mcp": True
        },
        {
            "question": "Show me my current AWS costs and spending breakdown",
            "category": "Costs",
            "should_use_mcp": True
        },
        {
            "question": "What is AWS Lambda and how does it work?",
            "category": "Regular",
            "should_use_mcp": False
        }
    ]
    
    print("🔍 Testing Enhanced MCP Queries")
    print("=" * 60)
    print("🎯 Testing inventory and cost detection with new MCP tools")
    print("=" * 60)
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{i}. {query['category']} Query:")
        print(f"   Question: {query['question']}")
        print(f"   Expected MCP: {'YES' if query['should_use_mcp'] else 'NO'}")
        print("-" * 50)
        
        # Test with troubleshooting endpoint
        result = make_request(f"{backend_url}/query/troubleshoot", query['question'])
        
        if result and not result.get('error'):
            mcp_enhanced = result.get('enhanced_with_mcp', False)
            aws_data = result.get('aws_real_time_data', False)
            troubleshooting_mode = result.get('troubleshooting_mode', False)
            
            print(f"   ✅ Status: SUCCESS")
            print(f"   🔧 MCP Enhanced: {'YES ✅' if mcp_enhanced else 'NO ❌'}")
            print(f"   ☁️  Real-time AWS Data: {'YES ✅' if aws_data else 'NO ❌'}")
            print(f"   🚨 Troubleshooting Mode: {'YES ✅' if troubleshooting_mode else 'NO ❌'}")
            
            # Check if behavior matches expectation
            if mcp_enhanced == query['should_use_mcp']:
                print(f"   ✅ Behavior: As expected")
            else:
                print(f"   ⚠️  Behavior: Unexpected (got MCP={mcp_enhanced}, expected={query['should_use_mcp']})")
            
            # Show response preview
            answer = result.get('answer', '')
            if answer:
                print(f"   📝 Response preview:")
                print(f"      {answer[:200]}...")
                
                # Look for specific data types in inventory/cost responses
                if query['category'] == 'Inventory' and mcp_enhanced:
                    if any(term in answer.lower() for term in ['instance', 'running', 'count', 'status']):
                        print(f"   🔍 Contains inventory data: ✅")
                elif query['category'] == 'Costs' and mcp_enhanced:
                    if any(term in answer.lower() for term in ['cost', 'billing', 'spending', 'price']):
                        print(f"   💰 Contains cost data: ✅")
        else:
            print(f"   ❌ Failed: {result.get('error', 'Unknown error') if result else 'No response'}")
        
        time.sleep(2)

def make_request(url: str, question: str):
    """Make a request to the API"""
    try:
        response = requests.post(
            url,
            json={
                "question": question,
                "retrieval_config": {
                    "retriever_type": "multi_vector",
                    "use_compression": True,
                    "similarity_threshold": 0.3
                }
            },
            headers={"Content-Type": "application/json"},
            timeout=90
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": f"HTTP {response.status_code}"}
            
    except Exception as e:
        return {"error": str(e)}

def show_usage_guide():
    """Show how to use the enhanced features"""
    print(f"\n{'='*60}")
    print("📖 How to Ask Inventory & Cost Questions")
    print("=" * 60)
    print("""
🔍 **Inventory Questions** (MCP Enhanced):
   • "How many EC2 instances do I have?"
   • "List all my running instances"
   • "What AWS services am I using?"
   • "Show me my current infrastructure status"
   • "What instances are running right now?"

💰 **Cost Questions** (MCP Enhanced):
   • "What are my current AWS costs?"
   • "How much am I spending on EC2?"
   • "Show me my billing breakdown"
   • "Which services are most expensive?"
   • "What are my cost optimization opportunities?"

🚨 **Troubleshooting Questions** (MCP Enhanced):
   • "My EC2 instance is running slowly"
   • "I'm seeing 500 errors in my logs"
   • "High CPU usage on my instances"
   • "Lambda functions are timing out"

💡 **Regular Questions** (Standard RAG):
   • "What is AWS Lambda?"
   • "How do I configure S3 buckets?"
   • "Compare EC2 and ECS"

🎯 **Frontend Usage**:
   1. Go to http://localhost:8000
   2. Type any of the above questions naturally
   3. Look for MCP enhancement indicators:
      • "🚨 MCP-Enhanced Response"
      • "☁️ Real-time AWS infrastructure data"
      • "MCP Enhanced: ✅ YES"

🔧 **What You'll Get**:
   • Real-time instance counts and status
   • Live AWS resource inventory
   • Cost estimates and spending analysis
   • Infrastructure health assessment
   • Optimization recommendations
""")

def main():
    """Main function"""
    print("🚀 Enhanced MCP Integration Test")
    print("=" * 60)
    
    # Test the enhanced queries
    test_enhanced_queries()
    
    # Show usage guide
    show_usage_guide()
    
    print(f"\n{'='*60}")
    print("🎉 Enhanced MCP Features Ready!")
    print("=" * 60)
    print("✅ Your system now supports:")
    print("   • Automatic inventory queries with real-time data")
    print("   • Cost analysis with resource-based estimates")
    print("   • Enhanced troubleshooting with live metrics")
    print("   • Smart query detection and routing")
    print("\n🔗 Frontend: http://localhost:8000")
    print("🔗 Backend: http://localhost:8889")

if __name__ == "__main__":
    main()
