#!/usr/bin/env python3
"""
Test inventory and cost queries with MCP integration
"""

import requests
import json
import time

def test_inventory_and_cost_queries():
    """Test inventory and cost-related queries"""
    
    backend_url = "http://localhost:8888"
    
    # Test queries about your infrastructure
    test_queries = [
        {
            "question": "How many EC2 instances do I have and what are their types?",
            "category": "Inventory"
        },
        {
            "question": "List all my running AWS instances and their current status",
            "category": "Inventory"
        },
        {
            "question": "What AWS services am I currently using?",
            "category": "Inventory"
        },
        {
            "question": "Show me my current AWS costs and which services are most expensive",
            "category": "Costs"
        },
        {
            "question": "How much am I spending on EC2 instances this month?",
            "category": "Costs"
        },
        {
            "question": "What are my AWS billing trends and cost optimization opportunities?",
            "category": "Costs"
        }
    ]
    
    print("🔍 Testing Inventory & Cost Queries with MCP")
    print("=" * 60)
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{i}. {query['category']} Query:")
        print(f"   Question: {query['question']}")
        print("-" * 50)
        
        # Test with troubleshooting endpoint (should provide real-time data)
        result = make_request(f"{backend_url}/query/troubleshoot", query['question'])
        
        if result and not result.get('error'):
            mcp_enhanced = result.get('enhanced_with_mcp', False)
            aws_data = result.get('aws_real_time_data', False)
            
            print(f"   ✅ Status: SUCCESS")
            print(f"   🔧 MCP Enhanced: {'YES' if mcp_enhanced else 'NO'}")
            print(f"   ☁️  Real-time AWS Data: {'YES' if aws_data else 'NO'}")
            
            # Show response preview
            answer = result.get('answer', '')
            if answer:
                print(f"   📝 Response preview:")
                print(f"      {answer[:200]}...")
        else:
            print(f"   ❌ Failed: {result.get('error', 'Unknown error') if result else 'No response'}")
        
        time.sleep(2)

def make_request(url: str, question: str):
    """Make a request to the API"""
    try:
        response = requests.post(
            url,
            json={
                "question": question,
                "retrieval_config": {
                    "retriever_type": "multi_vector",
                    "use_compression": True,
                    "similarity_threshold": 0.3
                }
            },
            headers={"Content-Type": "application/json"},
            timeout=90
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": f"HTTP {response.status_code}"}
            
    except Exception as e:
        return {"error": str(e)}

def show_current_inventory():
    """Show what we discovered about your current AWS inventory"""
    print(f"\n{'='*60}")
    print("📊 Your Current AWS Inventory (from previous discovery)")
    print("=" * 60)
    print("""
✅ **EC2 Instances**: 2 instances
   • i-0d0faa5b7e20d4bf1 (and 1 more)
   • Mix of instance types and states

✅ **Lambda Functions**: 1 function
   • Available for monitoring and cost analysis

✅ **S3 Buckets**: 5 buckets
   • Including 'knowledge' bucket for your RAG system

❌ **RDS Instances**: 0 instances
   • No database instances currently running

🔍 **Cost Monitoring Capabilities**:
   • CloudWatch metrics for usage patterns
   • Cost Explorer data (if enabled)
   • Resource utilization analysis
   • Optimization recommendations
""")

def main():
    """Main function"""
    print("💰 AWS Inventory & Cost Analysis with MCP")
    print("=" * 60)
    
    # Show current inventory
    show_current_inventory()
    
    # Test queries
    test_inventory_and_cost_queries()
    
    print(f"\n{'='*60}")
    print("🎯 How to Ask These Questions")
    print("=" * 60)
    print("""
💬 **In the Frontend** (http://localhost:8000):
   Just type naturally:
   • "How many instances do I have?"
   • "What are my AWS costs?"
   • "Show me my EC2 usage"
   • "Which services cost the most?"

🔧 **MCP Enhancement**:
   • Real-time instance counts and status
   • Live cost data from AWS APIs
   • Resource utilization metrics
   • Cost optimization suggestions

📊 **Available Data**:
   • EC2 instance inventory and costs
   • Lambda function usage and billing
   • S3 storage costs and usage
   • CloudWatch metrics for optimization
""")

if __name__ == "__main__":
    main()
