#!/bin/bash

# Setup script for MCP (Model Context Protocol) AWS Integration
# This script configures MCP for production troubleshooting with read-only AWS access

set -e

echo "🚀 Setting up MCP AWS Integration for RAG System"
echo "================================================"

# Check if running from correct directory
if [ ! -f "requirements.txt" ]; then
    echo "❌ Error: Please run this script from the RAG_Quadrant root directory"
    exit 1
fi

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo "❌ Error: AWS CLI is not installed. Please install it first."
    echo "   Visit: https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html"
    exit 1
fi

# Check AWS credentials
echo "🔍 Checking AWS credentials..."
if ! aws sts get-caller-identity &> /dev/null; then
    echo "❌ Error: AWS credentials not configured or invalid"
    echo "   Run: aws configure"
    exit 1
fi

AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
AWS_REGION=$(aws configure get region || echo "us-east-1")
echo "✅ AWS Account: $AWS_ACCOUNT_ID, Region: $AWS_REGION"

# Install MCP dependencies
echo "📦 Installing MCP dependencies..."
pip install mcp

# Create IAM policy for MCP read-only access
echo "🔐 Creating IAM policy for MCP read-only access..."

POLICY_NAME="MCPReadOnlyTroubleshooting"
POLICY_DOCUMENT='{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "CloudWatchReadOnly",
      "Effect": "Allow",
      "Action": [
        "cloudwatch:Describe*",
        "cloudwatch:Get*",
        "cloudwatch:List*",
        "logs:Describe*",
        "logs:Get*",
        "logs:Filter*",
        "logs:StartQuery",
        "logs:StopQuery",
        "logs:TestMetricFilter"
      ],
      "Resource": "*"
    },
    {
      "Sid": "EC2ReadOnly",
      "Effect": "Allow",
      "Action": [
        "ec2:Describe*",
        "ec2:Get*"
      ],
      "Resource": "*"
    },
    {
      "Sid": "ECSReadOnly",
      "Effect": "Allow",
      "Action": [
        "ecs:Describe*",
        "ecs:List*"
      ],
      "Resource": "*"
    },
    {
      "Sid": "RDSReadOnly",
      "Effect": "Allow",
      "Action": [
        "rds:Describe*",
        "rds:List*"
      ],
      "Resource": "*"
    },
    {
      "Sid": "LambdaReadOnly",
      "Effect": "Allow",
      "Action": [
        "lambda:Get*",
        "lambda:List*"
      ],
      "Resource": "*"
    },
    {
      "Sid": "ApplicationInsights",
      "Effect": "Allow",
      "Action": [
        "application-insights:Describe*",
        "application-insights:List*"
      ],
      "Resource": "*"
    },
    {
      "Sid": "XRayReadOnly",
      "Effect": "Allow",
      "Action": [
        "xray:Get*",
        "xray:BatchGet*"
      ],
      "Resource": "*"
    }
  ]
}'

# Create the policy (ignore error if it already exists)
aws iam create-policy \
    --policy-name "$POLICY_NAME" \
    --policy-document "$POLICY_DOCUMENT" \
    --description "Read-only access for MCP AWS troubleshooting integration" \
    2>/dev/null || echo "ℹ️  Policy $POLICY_NAME already exists"

# Create IAM role for MCP
echo "👤 Creating IAM role for MCP..."

ROLE_NAME="MCPTroubleshootingRole"
TRUST_POLICY='{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::'$AWS_ACCOUNT_ID':root"
      },
      "Action": "sts:AssumeRole",
      "Condition": {
        "StringEquals": {
          "sts:ExternalId": "mcp-troubleshooting"
        }
      }
    }
  ]
}'

# Create the role (ignore error if it already exists)
aws iam create-role \
    --role-name "$ROLE_NAME" \
    --assume-role-policy-document "$TRUST_POLICY" \
    --description "Role for MCP AWS troubleshooting with read-only access" \
    2>/dev/null || echo "ℹ️  Role $ROLE_NAME already exists"

# Attach policy to role
aws iam attach-role-policy \
    --role-name "$ROLE_NAME" \
    --policy-arn "arn:aws:iam::$AWS_ACCOUNT_ID:policy/$POLICY_NAME"

echo "✅ IAM role and policy created successfully"

# Update environment variables
echo "🔧 Updating environment configuration..."

ENV_FILE=".env"
if [ ! -f "$ENV_FILE" ]; then
    echo "❌ Error: .env file not found. Please create it first."
    exit 1
fi

# Add MCP configuration to .env if not already present
if ! grep -q "MCP_ENABLED" "$ENV_FILE"; then
    echo "" >> "$ENV_FILE"
    echo "# MCP (Model Context Protocol) Configuration" >> "$ENV_FILE"
    echo "MCP_ENABLED=true" >> "$ENV_FILE"
    echo "MCP_AWS_ROLE_ARN=arn:aws:iam::$AWS_ACCOUNT_ID:role/$ROLE_NAME" >> "$ENV_FILE"
    echo "MCP_EXTERNAL_ID=mcp-troubleshooting" >> "$ENV_FILE"
    echo "MCP_SERVER_PATH=Backend/mcp_aws_server.py" >> "$ENV_FILE"
    echo "✅ MCP configuration added to .env file"
else
    echo "ℹ️  MCP configuration already exists in .env file"
fi

# Test MCP server
echo "🧪 Testing MCP server..."
cd Backend
if python -c "import mcp_aws_server; print('MCP server imports successfully')"; then
    echo "✅ MCP server test passed"
else
    echo "❌ MCP server test failed"
    exit 1
fi
cd ..

# Update Chainlit configuration for MCP
echo "🔧 Updating Chainlit MCP configuration..."
CHAINLIT_CONFIG=".chainlit/config.toml"
if [ -f "$CHAINLIT_CONFIG" ]; then
    # Ensure MCP is enabled in Chainlit config
    if grep -q "\[features.mcp.stdio\]" "$CHAINLIT_CONFIG"; then
        sed -i 's/enabled = false/enabled = true/g' "$CHAINLIT_CONFIG" 2>/dev/null || true
        echo "✅ Chainlit MCP configuration updated"
    else
        echo "ℹ️  Chainlit MCP configuration already present"
    fi
else
    echo "⚠️  Warning: Chainlit config file not found at $CHAINLIT_CONFIG"
fi

echo ""
echo "🎉 MCP AWS Integration Setup Complete!"
echo "======================================"
echo ""
echo "Next steps:"
echo "1. Restart your RAG system: python Backend/main.py"
echo "2. Test the troubleshooting endpoint: POST /query/troubleshoot"
echo "3. Configure specific log groups in Backend/mcp_config.json"
echo ""
echo "Available MCP tools:"
echo "- get_cloudwatch_metrics: Retrieve CloudWatch metrics"
echo "- query_cloudwatch_logs: Search CloudWatch logs for errors"
echo "- get_ec2_instance_health: Check EC2 instance status"
echo "- get_rds_instance_status: Check RDS database status"
echo "- analyze_error_patterns: Analyze error patterns across services"
echo ""
echo "Security note: All MCP operations are read-only and cannot modify AWS resources."
