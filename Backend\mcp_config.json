{"mcp_aws_config": {"server_name": "aws-troubleshooting", "server_version": "1.0.0", "description": "MCP server for AWS production troubleshooting with read-only access", "capabilities": {"tools": true, "resources": true, "prompts": false, "logging": true}, "aws_services": {"cloudwatch": {"enabled": true, "default_namespace": "AWS/EC2", "default_metrics": ["CPUUtilization", "NetworkIn", "NetworkOut", "DiskReadOps", "DiskWriteOps"], "log_groups": ["/aws/lambda/your-function-name", "/aws/ec2/your-application", "/aws/ecs/your-cluster"]}, "ec2": {"enabled": true, "monitor_instances": true, "include_tags": true}, "rds": {"enabled": true, "monitor_instances": true, "include_performance_insights": false}, "lambda": {"enabled": true, "monitor_functions": true, "include_logs": true}, "ecs": {"enabled": true, "monitor_clusters": true, "monitor_services": true}, "application_insights": {"enabled": true, "monitor_applications": true}}, "security": {"read_only": true, "allowed_actions": ["cloudwatch:Describe*", "cloudwatch:Get*", "cloudwatch:List*", "logs:Describe*", "logs:Get*", "logs:Filter*", "logs:StartQuery", "logs:StopQuery", "ec2:Describe*", "ec2:Get*", "rds:Describe*", "rds:List*", "lambda:Get*", "lambda:List*", "ecs:Describe*", "ecs:List*", "application-insights:Describe*", "application-insights:List*", "xray:Get*", "xray:BatchGet*"], "denied_actions": ["*:Create*", "*:Delete*", "*:Update*", "*:Modify*", "*:Put*", "*:Start*", "*:Stop*", "*:Reboot*", "*:Terminate*"]}, "query_defaults": {"time_range_hours": 1, "max_log_entries": 100, "max_metrics_datapoints": 100, "metric_period_seconds": 300}, "integration": {"rag_system": {"enabled": true, "combine_contexts": true, "prioritize_real_time_data": true, "fallback_to_rag_only": true}, "troubleshooting_keywords": ["error", "issue", "problem", "fail", "slow", "down", "not working", "troubleshoot", "debug", "fix", "broken", "performance", "latency", "timeout", "crash", "exception", "alert", "alarm"]}}}