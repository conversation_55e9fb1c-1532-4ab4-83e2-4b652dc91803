#!/usr/bin/env python3
"""
Test the enhanced MCP integration with AWS Pricing and Cost Explorer
"""

import requests
import json
import time

def test_enhanced_mcp():
    """Test the enhanced MCP integration with pricing and cost explorer"""
    
    backend_url = "http://localhost:8888"
    
    test_cases = [
        {
            "name": "Basic Inventory Query",
            "question": "How many EC2 instances do I have?",
            "should_use_mcp": True,
            "endpoint": "/query/aws-live",
            "expected_features": ["inventory"]
        },
        {
            "name": "Cost Analysis Query",
            "question": "What are my AWS costs and spending patterns?",
            "should_use_mcp": True,
            "endpoint": "/query/aws-live",
            "expected_features": ["costs", "cost_explorer"]
        },
        {
            "name": "Pricing Query",
            "question": "What is the pricing for EC2 instances?",
            "should_use_mcp": True,
            "endpoint": "/query/aws-live",
            "expected_features": ["pricing"]
        },
        {
            "name": "S3 Pricing Query",
            "question": "How much does S3 storage cost?",
            "should_use_mcp": True,
            "endpoint": "/query/aws-live",
            "expected_features": ["pricing"]
        },
        {
            "name": "Budget Planning Query",
            "question": "What will my AWS bill be next month?",
            "should_use_mcp": True,
            "endpoint": "/query/aws-live",
            "expected_features": ["cost_explorer"]
        }
    ]
    
    print("🚀 Testing Enhanced MCP Integration")
    print("=" * 60)
    print("🎯 Testing AWS Pricing + Cost Explorer + Infrastructure Data")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print(f"   Question: {test_case['question']}")
        print(f"   Expected Features: {', '.join(test_case['expected_features'])}")
        print("-" * 50)
        
        result = make_request(f"{backend_url}{test_case['endpoint']}", test_case['question'])
        
        if result and not result.get('error'):
            mcp_enhanced = result.get('enhanced_with_mcp', False)
            aws_data = result.get('aws_real_time_data', False)
            troubleshooting_mode = result.get('troubleshooting_mode', False)
            
            print(f"   ✅ Status: SUCCESS")
            print(f"   🔧 MCP Enhanced: {'YES ✅' if mcp_enhanced else 'NO ❌'}")
            print(f"   ☁️  AWS Real-time Data: {'YES ✅' if aws_data else 'NO ❌'}")
            print(f"   🚨 Troubleshooting Mode: {'YES ✅' if troubleshooting_mode else 'NO ❌'}")
            
            # Check for enhanced features in the response
            answer = result.get('answer', '')
            features_found = []
            
            if "Real-time AWS Infrastructure Data" in answer:
                features_found.append("infrastructure")
            if "EC2 Instances:" in answer:
                features_found.append("inventory")
            if "Detailed Cost Analysis:" in answer:
                features_found.append("cost_explorer")
            if "Current Pricing Information:" in answer:
                features_found.append("pricing")
            if "30-Day Forecast:" in answer:
                features_found.append("forecast")
            if "Total Cost" in answer:
                features_found.append("costs")
            
            print(f"   🔍 Features Found: {', '.join(features_found) if features_found else 'None'}")
            
            # Check if expected features are present
            expected_found = any(feature in features_found for feature in test_case['expected_features'])
            print(f"   ✅ Expected Features: {'FOUND ✅' if expected_found else 'MISSING ❌'}")
            
            # Show response preview
            if answer:
                print(f"   📝 Response preview:")
                print(f"      {answer[:300]}...")
                
        else:
            print(f"   ❌ Failed: {result.get('error', 'Unknown error') if result else 'No response'}")
        
        time.sleep(3)  # Longer delay to avoid rate limits

def test_specific_features():
    """Test specific enhanced features"""
    
    backend_url = "http://localhost:8888"
    
    print(f"\n{'='*60}")
    print("🔬 Testing Specific Enhanced Features")
    print("=" * 60)
    
    # Test comprehensive cost query
    print("\n🔍 Testing Comprehensive Cost Analysis...")
    result = make_request(f"{backend_url}/query/aws-live", 
                         "Give me a complete breakdown of my AWS costs, spending patterns, and forecast")
    
    if result and not result.get('error'):
        answer = result.get('answer', '')
        
        # Check for all cost-related features
        cost_features = {
            "Infrastructure Data": "Real-time AWS Infrastructure Data" in answer,
            "Cost Estimates": "Cost Estimates:" in answer,
            "Detailed Cost Analysis": "Detailed Cost Analysis:" in answer,
            "Cost Forecast": "30-Day Forecast:" in answer,
            "Service Breakdown": "Top Services by Cost" in answer,
            "Total Cost": "Total Cost" in answer
        }
        
        print("   Cost Analysis Features:")
        for feature, found in cost_features.items():
            status = "✅" if found else "❌"
            print(f"   - {feature}: {status}")
        
        # Show cost data if available
        if "Total Cost" in answer:
            print("   💰 Cost data successfully retrieved!")
    else:
        print("   ❌ Cost analysis test failed")

def make_request(url: str, question: str):
    """Make a request to the API"""
    try:
        response = requests.post(
            url,
            json={
                "question": question,
                "retrieval_config": {
                    "retriever_type": "multi_vector",
                    "use_compression": True,
                    "similarity_threshold": 0.3
                }
            },
            headers={"Content-Type": "application/json"},
            timeout=120  # Longer timeout for cost explorer calls
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": f"HTTP {response.status_code}"}
            
    except Exception as e:
        return {"error": str(e)}

def main():
    """Main function"""
    test_enhanced_mcp()
    test_specific_features()
    
    print(f"\n{'='*60}")
    print("🎯 Enhanced MCP Integration Test Complete!")
    print("=" * 60)
    print("""
✅ **Enhanced Features Added:**
   • AWS Pricing API integration for real-time pricing data
   • Cost Explorer integration for historical cost analysis
   • Cost forecasting and budget planning
   • Service-specific pricing queries
   • Detailed cost breakdowns by service
   • 30-day cost forecasts

🔗 **Frontend Usage:**
   Go to http://localhost:8000 and try:
   • "What are my AWS costs?"
   • "How much does EC2 cost?"
   • "What will my AWS bill be next month?"
   • "Show me my spending by service"
   
   You should see enhanced responses with:
   ☁️ AWS Live Response headers
   📊 Detailed Cost Analysis sections
   💲 Current Pricing Information
   💰 Cost forecasts and estimates

📊 **New Capabilities:**
   • Real-time pricing from AWS Pricing API
   • Historical cost analysis from Cost Explorer
   • Cost forecasting and trend analysis
   • Service-specific cost breakdowns
   • Budget planning assistance
""")

if __name__ == "__main__":
    main()
