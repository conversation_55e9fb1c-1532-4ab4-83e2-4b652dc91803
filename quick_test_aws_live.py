#!/usr/bin/env python3
"""
Quick test of AWS Live endpoint
"""

import requests
import json

def test_ec2_query():
    """Test the EC2 instance query"""
    
    url = "http://localhost:8889/query/aws-live"
    question = "How many EC2 instances do I have?"
    
    payload = {
        "question": question,
        "retrieval_config": {
            "retriever_type": "multi_vector",
            "use_compression": True,
            "similarity_threshold": 0.3
        }
    }
    
    print(f"🔍 Testing: {question}")
    print("=" * 50)
    
    try:
        response = requests.post(
            url,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"✅ Status: SUCCESS")
            print(f"🔧 MCP Enhanced: {result.get('enhanced_with_mcp', False)}")
            print(f"☁️  AWS Real-time Data: {result.get('aws_real_time_data', False)}")
            print(f"🚨 Troubleshooting Mode: {result.get('troubleshooting_mode', False)}")
            
            answer = result.get('answer', '')
            print(f"\n📝 Response:")
            print(answer[:500] + "..." if len(answer) > 500 else answer)
            
            # Check for real-time data indicators
            if "Real-time AWS Infrastructure Data" in answer:
                print(f"\n🔍 ✅ Contains real-time AWS data section")
            if "EC2 Instances:" in answer:
                print(f"📊 ✅ Contains EC2 inventory")
            
        else:
            print(f"❌ Failed: HTTP {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_ec2_query()
