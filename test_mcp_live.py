#!/usr/bin/env python3
"""
Live test of MCP RAG integration
Tests both regular queries and troubleshooting queries to demonstrate MCP enhancement
"""

import requests
import json
import time
from typing import Dict, Any

# Backend URL
BASE_URL = "http://localhost:8888"

def test_query(question: str, query_type: str = "regular") -> Dict[str, Any]:
    """Test a query against the RAG system"""
    
    print(f"\n{'='*60}")
    print(f"🔍 Testing {query_type.upper()} Query")
    print(f"{'='*60}")
    print(f"Question: {question}")
    print(f"{'='*60}")
    
    # Use the troubleshooting endpoint for troubleshooting queries
    if "troubleshoot" in query_type.lower() or "issue" in query_type.lower():
        endpoint = f"{BASE_URL}/query/troubleshoot"
    else:
        endpoint = f"{BASE_URL}/query/advanced"
    
    payload = {
        "question": question,
        "retrieval_config": {
            "retriever_type": "multi_vector",
            "use_compression": True,
            "similarity_threshold": 0.3
        }
    }
    
    try:
        print("⏳ Sending request...")
        start_time = time.time()
        
        response = requests.post(
            endpoint,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"⏱️  Response time: {response_time:.2f} seconds")
        
        if response.status_code == 200:
            result = response.json()
            
            # Check if MCP enhancement was used
            mcp_enhanced = result.get("enhanced_with_mcp", False)
            aws_data = result.get("aws_real_time_data", False)
            troubleshooting_mode = result.get("troubleshooting_mode", False)
            
            print(f"✅ Status: SUCCESS")
            print(f"🔧 MCP Enhanced: {'YES' if mcp_enhanced else 'NO'}")
            print(f"☁️  AWS Real-time Data: {'YES' if aws_data else 'NO'}")
            print(f"🚨 Troubleshooting Mode: {'YES' if troubleshooting_mode else 'NO'}")
            
            # Display the answer
            answer = result.get("answer", "No answer provided")
            print(f"\n📝 Answer:")
            print(f"{'-'*40}")
            print(answer[:500] + "..." if len(answer) > 500 else answer)
            print(f"{'-'*40}")
            
            # Show sources if available
            sources = result.get("sources", [])
            if sources:
                print(f"\n📚 Sources ({len(sources)}):")
                for i, source in enumerate(sources[:3], 1):
                    print(f"  {i}. {source.get('source', 'Unknown')}")
            
            return result
            
        else:
            print(f"❌ Error: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            return {"error": f"HTTP {response.status_code}", "details": response.text}
            
    except requests.exceptions.Timeout:
        print("❌ Error: Request timed out")
        return {"error": "timeout"}
    except requests.exceptions.ConnectionError:
        print("❌ Error: Could not connect to backend")
        return {"error": "connection_error"}
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return {"error": str(e)}

def main():
    """Run MCP integration tests"""
    
    print("🧪 MCP RAG Integration Live Test")
    print("=" * 60)
    
    # Test 1: Regular query (should NOT use MCP)
    regular_result = test_query(
        "What is AWS Lambda and how does it work?",
        "regular"
    )
    
    time.sleep(2)
    
    # Test 2: Troubleshooting query (should use MCP)
    troubleshoot_result = test_query(
        "My EC2 instance is running slowly and CPU usage is high. What should I check?",
        "troubleshooting"
    )
    
    time.sleep(2)
    
    # Test 3: Another troubleshooting query
    error_result = test_query(
        "I'm seeing 500 errors in my application logs. How do I troubleshoot this?",
        "troubleshooting"
    )
    
    time.sleep(2)
    
    # Test 4: Performance issue query
    performance_result = test_query(
        "My database connections are timing out. What could be the problem?",
        "troubleshooting"
    )
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 TEST SUMMARY")
    print(f"{'='*60}")
    
    tests = [
        ("Regular Query", regular_result),
        ("EC2 Performance Issue", troubleshoot_result),
        ("Application Errors", error_result),
        ("Database Timeout", performance_result)
    ]
    
    for test_name, result in tests:
        if "error" not in result:
            mcp_status = "✅ MCP Enhanced" if result.get("enhanced_with_mcp") else "⚪ Regular RAG"
            print(f"{test_name:25} | {mcp_status}")
        else:
            print(f"{test_name:25} | ❌ Failed")
    
    print(f"\n🎯 Expected Behavior:")
    print(f"  • Regular queries should use standard RAG (⚪)")
    print(f"  • Troubleshooting queries should use MCP enhancement (✅)")
    print(f"\n🔗 Backend running at: {BASE_URL}")

if __name__ == "__main__":
    main()
