"""
Image processing and analysis service.
"""
import os
import logging
import time
from typing import Dict, Any, Optional, List
from PIL import Image
import networkx as nx

from .models import ImageAnalysisRequest, ImageAnalysisResult, ImageType, AnalysisStatus, Analysis, AnalysisDetail, BoundingBox, Recommendation
from .storage import TemporaryImageStore, AnalysisResultStore
from .ocr import OCRService, OCRLanguage, OCRMode, OCRPreprocessing
from .error_recognition import ErrorRecognitionService, ErrorSeverity, ErrorCategory
from .diagram_text import DiagramTextRecognitionService, TextElement, TextAlignment
from .classifier import ImageTypeClassifier
from .diagram_components import DiagramComponentRecognitionService, ComponentType
from .orchestrator import AnalysisOrchestrator


# Configure logging
logger = logging.getLogger(__name__)


class ImageProcessor:
    """
    Service for processing and analyzing images.
    """
    
    def __init__(self, image_store: TemporaryImageStore, result_store: AnalysisResultStore):
        """
        Initialize the image processor.
        
        Args:
            image_store: Storage service for images
            result_store: Storage service for analysis results
        """
        self.image_store = image_store
        self.result_store = result_store
        
        # Initialize OCR service with default configuration
        self.ocr_service = OCRService({
            "language": OCRLanguage.ENGLISH,
            "mode": OCRMode.DEFAULT,
            "preprocessing": OCRPreprocessing.NONE,
            "confidence_threshold": 0
        })
        
        # Initialize error recognition service
        self.error_recognition_service = ErrorRecognitionService()
        
        # Initialize diagram text recognition service
        self.diagram_text_service = DiagramTextRecognitionService(self.ocr_service)
        
        # Initialize image type classifier
        self.image_classifier = ImageTypeClassifier(self.ocr_service)
        
        # Initialize diagram component recognition service
        self.diagram_component_service = DiagramComponentRecognitionService(self.diagram_text_service)
        
        # Initialize analysis orchestrator
        self.orchestrator = AnalysisOrchestrator(
            image_store=image_store,
            result_store=result_store,
            ocr_service=self.ocr_service,
            error_recognition_service=self.error_recognition_service,
            diagram_text_service=self.diagram_text_service,
            image_classifier=self.image_classifier,
            diagram_component_service=self.diagram_component_service
        )
    
    async def process_image(self, request_id: str) -> bool:
        """
        Process an image analysis request using the analysis orchestrator.
        
        Args:
            request_id: ID of the analysis request
            
        Returns:
            bool: True if processing was successful
        """
        logger.info(f"Processing image analysis request: {request_id}")
        
        # Delegate processing to the orchestrator
        success = await self.orchestrator.process_request(request_id)
        
        if success:
            logger.info(f"Successfully processed image: {request_id}")
        else:
            logger.error(f"Failed to process image: {request_id}")
            
        return success
    
    def extract_text(self, image_path: str) -> str:
        """
        Extract text from an image using OCR.
        
        Args:
            image_path: Path to the image
            
        Returns:
            str: Extracted text
        """
        try:
            # Determine the appropriate OCR mode and preprocessing based on file extension
            file_ext = os.path.splitext(image_path)[1].lower() if image_path else ""
            
            # Use different preprocessing for different image types
            preprocessing = OCRPreprocessing.NONE
            mode = OCRMode.DEFAULT
            
            # For PNG images (often screenshots or diagrams), use enhanced preprocessing
            if file_ext == ".png":
                preprocessing = OCRPreprocessing.ALL
            # For JPEG images (often photos), use noise removal and contrast enhancement
            elif file_ext in [".jpg", ".jpeg"]:
                preprocessing = OCRPreprocessing.CONTRAST_ENHANCEMENT
            
            # Extract text using the OCR service
            text = self.ocr_service.extract_text(
                image_path=image_path,
                preprocessing=preprocessing,
                mode=mode
            )
            
            return text.strip()
        except Exception as e:
            logger.warning(f"Error extracting text from image: {str(e)}")
            return ""
            
    def extract_text_with_layout(self, image_path: str) -> Dict[str, Any]:
        """
        Extract text with layout information from an image.
        
        Args:
            image_path: Path to the image
            
        Returns:
            Dict[str, Any]: Extracted text with layout information
        """
        try:
            # Extract text with layout using the OCR service
            return self.ocr_service.extract_text_with_layout(
                image_path=image_path,
                preprocessing=OCRPreprocessing.ALL
            )
        except Exception as e:
            logger.warning(f"Error extracting text with layout from image: {str(e)}")
            return {"text": "", "blocks": []}
            
    def extract_tables(self, image_path: str) -> List[Dict[str, Any]]:
        """
        Extract tables from an image.
        
        Args:
            image_path: Path to the image
            
        Returns:
            List[Dict[str, Any]]: List of extracted tables
        """
        try:
            # Extract tables using the OCR service
            return self.ocr_service.extract_tables(image_path)
        except Exception as e:
            logger.warning(f"Error extracting tables from image: {str(e)}")
            return []
    
    def classify_image_type(self, image_path: str, text_content: str) -> ImageType:
        """
        Classify the image as architecture diagram or error screenshot.
        
        Args:
            image_path: Path to the image
            text_content: Extracted text from the image
            
        Returns:
            ImageType: Classification of the image
        """
        try:
            # Use the image classifier to determine the image type
            image_type, confidence, features = self.image_classifier.classify_image(image_path, text_content)
            
            logger.info(f"Image classified as {image_type.value} with confidence {confidence:.2f}")
            
            # Log the feature scores for debugging
            for feature, score in features.items():
                logger.debug(f"Feature {feature}: {score:.2f}")
            
            return image_type
            
        except Exception as e:
            logger.error(f"Error classifying image: {str(e)}")
            
            # Fall back to simple keyword-based classification if the classifier fails
            error_keywords = ["error", "exception", "failed", "warning", "alert", "critical"]
            architecture_keywords = ["diagram", "architecture", "flow", "component", "service", "system"]
            
            # Count occurrences of keywords
            error_count = sum(1 for keyword in error_keywords if keyword.lower() in text_content.lower())
            architecture_count = sum(1 for keyword in architecture_keywords if keyword.lower() in text_content.lower())
            
            # Classify based on keyword counts
            if error_count > architecture_count:
                return ImageType.ERROR
            elif architecture_count > 0:
                return ImageType.ARCHITECTURE
            else:
                return ImageType.UNKNOWN
    
    def analyze_image(self, image_path: str, text_content: str, image_type: ImageType) -> Analysis:
        """
        Generate analysis for an image.
        
        Args:
            image_path: Path to the image
            text_content: Extracted text from the image
            image_type: Type of the image
            
        Returns:
            Analysis: Analysis of the image
        """
        # Generate a simple analysis based on image type
        if image_type == ImageType.ARCHITECTURE:
            return self._analyze_architecture(image_path)
        elif image_type == ImageType.ERROR:
            return self._analyze_error(text_content)
        else:
            return Analysis(
                summary="Unable to determine the type of this image. It doesn't appear to be an architecture diagram or error screenshot.",
                details=[],
                recommendations=[]
            )
    
    def _analyze_architecture(self, image_path: str) -> Analysis:
        """
        Generate analysis for an architecture diagram.
        
        Args:
            image_path: Path to the image
            
        Returns:
            Analysis: Analysis of the architecture diagram
        """
        try:
            # Use the diagram component recognition service to extract components and connections
            components, connections = self.diagram_component_service.recognize_components(image_path)
            
            # Create a graph representation of the diagram
            diagram_graph = self.diagram_component_service.create_diagram_graph(components, connections)
            
            # Count components by type
            component_counts = {}
            for component in components:
                if component.component_type not in component_counts:
                    component_counts[component.component_type] = 0
                component_counts[component.component_type] += 1
            
            # Create summary
            summary = "This appears to be an architecture diagram."
            
            # Add component counts to summary
            total_components = len(components)
            if total_components > 0:
                summary += f" Identified {total_components} components"
                
                # Add details about component types
                component_type_details = []
                for component_type, count in component_counts.items():
                    if component_type != ComponentType.UNKNOWN:
                        component_type_details.append(f"{count} {component_type}s")
                
                if component_type_details:
                    summary += f" ({', '.join(component_type_details)})."
                else:
                    summary += "."
            
            # Add connection count to summary
            if connections:
                summary += f" Found {len(connections)} connections between components."
            
            # Create details from components
            details = []
            
            # Add component details
            for component in components[:5]:  # Limit to top 5 components
                if component.text:
                    # Ensure confidence is within valid range (0.0 to 1.0)
                    normalized_confidence = max(0.0, min(component.confidence, 1.0))

                    detail = AnalysisDetail(
                        type=component.component_type,
                        content=component.text,
                        confidence=normalized_confidence,
                        bounding_box=BoundingBox(
                            x=component.bounding_box.x,
                            y=component.bounding_box.y,
                            width=component.bounding_box.width,
                            height=component.bounding_box.height
                        )
                    )
                    details.append(detail)
            
            # Add connection details
            for connection in connections[:3]:  # Limit to top 3 connections
                if connection.text:
                    source_component = next((c for c in components if c.component_id == connection.source_id), None)
                    target_component = next((c for c in components if c.component_id == connection.target_id), None)

                    source_text = source_component.text if source_component else "Unknown"
                    target_text = target_component.text if target_component else "Unknown"

                    # Ensure confidence is within valid range (0.0 to 1.0)
                    normalized_confidence = max(0.0, min(connection.confidence, 1.0))

                    detail = AnalysisDetail(
                        type="connection",
                        content=f"{source_text} {connection.text} {target_text}",
                        confidence=normalized_confidence
                    )
                    details.append(detail)
            
            # Use the diagram text recognition service to extract labels for additional context
            labels = self.diagram_text_service.extract_labels(image_path)
            
            # Add title if available
            if labels.get("title"):
                title = labels["title"][0]
                # Ensure confidence is within valid range (0.0 to 1.0)
                normalized_confidence = max(0.0, min(title.confidence, 1.0))

                detail = AnalysisDetail(
                    type="title",
                    content=title.text,
                    confidence=normalized_confidence,
                    bounding_box=BoundingBox(
                        x=title.bbox.x,
                        y=title.bbox.y,
                        width=title.bbox.width,
                        height=title.bbox.height
                    )
                )
                details.append(detail)
            
            # Create recommendations
            recommendations = []

            # Add recommendation for improving component labels if many components lack text
            unlabeled_components = sum(1 for c in components if not c.text)
            if unlabeled_components > total_components / 3:  # If more than 1/3 of components are unlabeled
                from .models import Recommendation
                recommendation = Recommendation(
                    type="suggestion",
                    content="For a more detailed analysis, ensure all components are clearly labeled in the diagram.",
                    priority=1
                )
                recommendations.append(recommendation)

            # Add recommendation for improving connections if few are detected
            if len(connections) < total_components - 1:  # In a connected diagram, there should be at least n-1 connections
                recommendation = Recommendation(
                    type="suggestion",
                    content="Consider adding more explicit connection labels to clarify relationships between components.",
                    priority=2
                )
                recommendations.append(recommendation)

            # Add recommendation for annotations if few are detected
            annotation_count = len(labels.get("annotation", []))
            if annotation_count == 0:
                recommendation = Recommendation(
                    type="suggestion",
                    content="Adding explanatory annotations would improve understanding of the diagram's purpose and functionality.",
                    priority=3
                )
                recommendations.append(recommendation)
            
            return Analysis(
                summary=summary,
                details=details,
                recommendations=recommendations
            )
            
        except Exception as e:
            logger.error(f"Error analyzing architecture diagram: {str(e)}")
            
            # Fall back to simpler analysis using just the text recognition service
            labels = self.diagram_text_service.extract_labels(image_path)
            
            # Count components and connections
            component_count = len(labels.get("component", []))
            connection_count = len(labels.get("connection", []))
            annotation_count = len(labels.get("annotation", []))
            
            # Create summary
            summary = "This appears to be an architecture diagram."
            if component_count > 0:
                summary += f" Identified {component_count} potential components."
            if connection_count > 0:
                summary += f" Found {connection_count} connections between components."
            if annotation_count > 0:
                summary += f" The diagram includes {annotation_count} annotations or explanatory notes."
            
            # Create details from component labels
            details = []
            
            # Add component details
            for component in labels.get("component", [])[:5]:  # Limit to top 5 components
                # Ensure confidence is within valid range (0.0 to 1.0)
                normalized_confidence = max(0.0, min(component.confidence, 1.0))

                detail = AnalysisDetail(
                    type="component",
                    content=component.text,
                    confidence=normalized_confidence,
                    bounding_box=BoundingBox(
                        x=component.bbox.x,
                        y=component.bbox.y,
                        width=component.bbox.width,
                        height=component.bbox.height
                    )
                )
                details.append(detail)
            
            # Add connection details
            for connection in labels.get("connection", [])[:3]:  # Limit to top 3 connections
                # Ensure confidence is within valid range (0.0 to 1.0)
                normalized_confidence = max(0.0, min(connection.confidence, 1.0))

                detail = AnalysisDetail(
                    type="connection",
                    content=connection.text,
                    confidence=normalized_confidence,
                    bounding_box=BoundingBox(
                        x=connection.bbox.x,
                        y=connection.bbox.y,
                        width=connection.bbox.width,
                        height=connection.bbox.height
                    )
                )
                details.append(detail)
            
            # Add title if available
            if labels.get("title"):
                title = labels["title"][0]
                # Ensure confidence is within valid range (0.0 to 1.0)
                normalized_confidence = max(0.0, min(title.confidence, 1.0))

                detail = AnalysisDetail(
                    type="title",
                    content=title.text,
                    confidence=normalized_confidence,
                    bounding_box=BoundingBox(
                        x=title.bbox.x,
                        y=title.bbox.y,
                        width=title.bbox.width,
                        height=title.bbox.height
                    )
                )
                details.append(detail)
            
            # Create recommendations
            from .models import Recommendation
            recommendations = [
                Recommendation(
                    type="suggestion",
                    content="For a more detailed analysis, ensure all components are clearly labeled in the diagram.",
                    priority=1
                )
            ]
            
            return Analysis(
                summary=summary,
                details=details,
                recommendations=recommendations
            )
    
    def _analyze_error(self, text_content: str) -> Analysis:
        """
        Generate analysis for an error screenshot.
        
        Args:
            text_content: Extracted text from the image
            
        Returns:
            Analysis: Analysis of the error screenshot
        """
        # Use the error recognition service to identify errors
        identified_errors = self.error_recognition_service.identify_errors(text_content)
        
        # Get suggested solutions
        solutions = self.error_recognition_service.suggest_solutions(text_content)
        
        # Classify the overall error type
        severity, category, confidence = self.error_recognition_service.classify_error_type(text_content)
        
        # Create summary
        summary = "This appears to be an error screenshot."
        if identified_errors:
            summary += f" Identified {len(identified_errors)} potential error messages."
            if category != ErrorCategory.UNKNOWN:
                summary += f" The errors appear to be related to {category.value} issues."
            if severity != ErrorSeverity.UNKNOWN:
                summary += f" The overall severity is {severity.value}."
        
        # Create details from identified errors
        details = []
        for error in identified_errors[:5]:  # Limit to top 5 errors
            # Ensure confidence is within valid range (0.0 to 1.0)
            normalized_confidence = max(0.0, min(error["confidence"], 1.0))

            # Create error detail content with additional information
            content = error["text"]
            if error.get("description"):
                content += f" - {error['description']}"

            detail = AnalysisDetail(
                type="error",
                content=content,
                confidence=normalized_confidence
            )
            details.append(detail)
        
        # Create recommendations from suggested solutions
        recommendations = []
        for i, solution in enumerate(solutions[:3], 1):  # Limit to top 3 solutions
            recommendation = Recommendation(
                type="solution",
                content=solution["solution"],
                priority=i
            )
            recommendations.append(recommendation)

        # Add generic recommendations if no specific solutions were found
        if not recommendations:
            recommendations = [
                Recommendation(
                    type="solution",
                    content="Check application logs for more detailed error information.",
                    priority=1
                ),
                Recommendation(
                    type="solution",
                    content="Search for the specific error message in documentation or forums.",
                    priority=2
                )
            ]
        
        return Analysis(
            summary=summary,
            details=details,
            recommendations=recommendations
        )