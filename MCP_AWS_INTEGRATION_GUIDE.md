# MCP AWS Integration Guide for RAG System

## Overview

This guide explains how to integrate Model Context Protocol (MCP) with your RAG system to provide real-time AWS troubleshooting capabilities. The integration combines your existing documentation-based RAG system with live AWS data for comprehensive production issue diagnosis.

## What is MCP?

Model Context Protocol (MCP) is a standardized protocol that allows AI systems to securely connect to external data sources and tools. In this implementation, MCP provides:

- **Real-time AWS data access** for production troubleshooting
- **Read-only security** to ensure safe operations
- **Seamless integration** with your existing RAG pipeline
- **Enhanced context** by combining documentation with live system state

## Architecture Integration

### Current RAG System
```
User Query → FastAPI → QueryEngine → Qdrant Vector Store → AWS Bedrock LLM → Response
```

### Enhanced with MCP
```
User Query → FastAPI → MCPEnhancedQueryEngine → {
    ├── RAG Documentation (Qdrant + Bedrock)
    └── Real-time AWS Data (MCP Server)
} → Combined Context → Enhanced Response
```

## Benefits for Your RAG System

### 1. **Real-time Production Insights**
- Live CloudWatch metrics and logs
- Current AWS resource states
- Active alarms and alerts
- Performance data correlation

### 2. **Enhanced Troubleshooting**
- Combines documentation with actual system state
- Identifies discrepancies between expected and actual configurations
- Provides immediate assessment of production issues
- Offers data-driven recommendations

### 3. **Improved Context Quality**
- Reduces hallucination by grounding responses in real data
- Provides specific, actionable guidance
- Correlates symptoms with actual metrics
- Validates documentation against current state

### 4. **Security and Compliance**
- Read-only access ensures no accidental modifications
- IAM policies restrict access to diagnostic operations only
- Audit trail of all MCP operations
- Principle of least privilege implementation

## AWS Services and APIs Utilized

### Core Services for Production Troubleshooting

1. **Amazon CloudWatch**
   - **Metrics**: CPU, memory, disk, network utilization
   - **Logs**: Application logs, system logs, error patterns
   - **Alarms**: Active alerts and threshold breaches
   - **Insights**: Log analytics and pattern detection

2. **Amazon EC2**
   - **Instance Status**: Running, stopped, terminated states
   - **Health Checks**: System and instance reachability
   - **Resource Utilization**: Performance metrics
   - **Configuration**: Security groups, tags, metadata

3. **Amazon RDS**
   - **Database Status**: Available, maintenance, backup states
   - **Performance Metrics**: Connections, CPU, storage
   - **Configuration**: Engine version, instance class
   - **Backup Status**: Automated backup information

4. **AWS Lambda**
   - **Function Status**: Active, inactive, error states
   - **Execution Metrics**: Duration, errors, throttles
   - **Log Streams**: Function execution logs
   - **Configuration**: Runtime, memory, timeout settings

5. **Amazon ECS**
   - **Cluster Health**: Service status, task states
   - **Resource Utilization**: CPU, memory usage
   - **Service Events**: Deployment and scaling events
   - **Task Definitions**: Configuration and versions

6. **AWS X-Ray**
   - **Trace Data**: Request flow and latency
   - **Service Map**: Application dependencies
   - **Error Analysis**: Exception and fault rates
   - **Performance Insights**: Bottleneck identification

## Implementation Details

### MCP Server Components

1. **AWS Client Initialization**
   ```python
   # Uses existing AWS credentials from your .env file
   self.cloudwatch = self.aws_session.client('cloudwatch')
   self.logs = self.aws_session.client('logs')
   self.ec2 = self.aws_session.client('ec2')
   ```

2. **MCP Tools Registration**
   - `get_cloudwatch_metrics`: Retrieve performance metrics
   - `query_cloudwatch_logs`: Search logs for error patterns
   - `get_ec2_instance_health`: Check instance status
   - `get_rds_instance_status`: Monitor database health
   - `analyze_error_patterns`: Cross-service error analysis

3. **Integration Layer**
   ```python
   # Combines RAG with MCP data
   enhanced_context = self._combine_contexts(rag_result, aws_data)
   enhanced_response = await self._generate_enhanced_response(
       question, enhanced_context, rag_result
   )
   ```

### Query Processing Flow

1. **Question Analysis**
   - Detect troubleshooting intent
   - Identify relevant AWS services
   - Determine data requirements

2. **Parallel Data Retrieval**
   - RAG system retrieves documentation
   - MCP server fetches real-time AWS data
   - Both processes run concurrently

3. **Context Combination**
   - Merge documentation with live data
   - Prioritize real-time information
   - Maintain source attribution

4. **Enhanced Response Generation**
   - Use combined context for LLM prompt
   - Generate actionable recommendations
   - Include both theoretical and practical guidance

## Security Configuration

### IAM Policy (Read-Only)
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "cloudwatch:Describe*",
        "cloudwatch:Get*",
        "cloudwatch:List*",
        "logs:Describe*",
        "logs:Get*",
        "logs:Filter*",
        "ec2:Describe*",
        "rds:Describe*",
        "lambda:Get*",
        "lambda:List*"
      ],
      "Resource": "*"
    }
  ]
}
```

### Security Features
- **No Write Permissions**: Cannot modify any AWS resources
- **Audit Logging**: All MCP operations are logged
- **Role-Based Access**: Uses IAM roles with external ID
- **Principle of Least Privilege**: Minimal required permissions only

## Setup Instructions

### 1. Prerequisites
- AWS CLI configured with appropriate credentials
- Python 3.8+ with your existing RAG environment
- IAM permissions to create roles and policies

### 2. Installation
```bash
# Run the setup script
chmod +x Scripts/setup_mcp_integration.sh
./Scripts/setup_mcp_integration.sh
```

### 3. Configuration
```bash
# Update your .env file with MCP settings
MCP_ENABLED=true
MCP_AWS_ROLE_ARN=arn:aws:iam::YOUR_ACCOUNT:role/MCPTroubleshootingRole
MCP_EXTERNAL_ID=mcp-troubleshooting
```

### 4. Testing
```bash
# Test the new troubleshooting endpoint
curl -X POST http://localhost:8888/query/troubleshoot \
  -H "Content-Type: application/json" \
  -d '{"question": "Why is my EC2 instance running slowly?"}'
```

## Usage Examples

### Example 1: Performance Troubleshooting
**Query**: "My application is running slowly, what could be the issue?"

**Enhanced Response**:
- **Real-time Data**: Current CPU at 95%, memory at 80%
- **Documentation**: Performance optimization best practices
- **Recommendation**: Scale up instance or optimize application

### Example 2: Error Investigation
**Query**: "I'm seeing 500 errors in my application logs"

**Enhanced Response**:
- **Real-time Data**: Recent error logs from CloudWatch
- **Documentation**: Common causes of 500 errors
- **Recommendation**: Specific fixes based on actual error patterns

### Example 3: Database Issues
**Query**: "My RDS database seems to be having connection issues"

**Enhanced Response**:
- **Real-time Data**: Current connection count, CPU usage
- **Documentation**: RDS troubleshooting procedures
- **Recommendation**: Connection pooling or instance scaling

## Monitoring and Maintenance

### Health Checks
- MCP server process monitoring
- AWS API rate limit tracking
- Error rate monitoring
- Response time metrics

### Configuration Updates
- Log group configuration in `mcp_config.json`
- Metric namespace customization
- Service-specific monitoring rules
- Alert threshold adjustments

## Troubleshooting

### Common Issues
1. **MCP Server Not Starting**: Check AWS credentials and permissions
2. **No Real-time Data**: Verify IAM role and policy attachment
3. **Slow Responses**: Check AWS API rate limits and network connectivity
4. **Missing Metrics**: Ensure CloudWatch agent is installed on instances

### Debug Mode
```bash
# Enable debug logging
export MCP_DEBUG=true
python Backend/main.py
```

## Future Enhancements

### Planned Features
- **Multi-region Support**: Query across multiple AWS regions
- **Custom Metrics**: Support for custom CloudWatch metrics
- **Predictive Analysis**: Trend analysis and forecasting
- **Integration Expansion**: Support for additional AWS services

### Extensibility
The MCP integration is designed to be extensible. You can:
- Add new AWS services by implementing additional MCP tools
- Customize query analysis logic for your specific use cases
- Integrate with third-party monitoring tools
- Extend the context combination algorithms

## Conclusion

The MCP AWS integration transforms your RAG system from a documentation-only assistant into a comprehensive production troubleshooting tool. By combining the depth of your documentation with the immediacy of real-time AWS data, you get:

- **Faster Issue Resolution**: Immediate access to relevant data
- **More Accurate Diagnoses**: Real data eliminates guesswork
- **Actionable Recommendations**: Specific, data-driven guidance
- **Enhanced User Experience**: Single interface for all troubleshooting needs

This integration maintains the security and reliability of your existing system while adding powerful new capabilities for production support.
