#!/usr/bin/env python3
"""
Test script to demonstrate MCP integration in the frontend
Shows how to query both regular and troubleshooting endpoints
"""

import requests
import json
import time

def test_frontend_integration():
    """Test the frontend integration with MCP"""
    
    backend_url = "http://localhost:8888"
    
    print("🚀 Testing Frontend MCP Integration")
    print("=" * 60)
    
    # Test cases that demonstrate the difference
    test_cases = [
        {
            "name": "Regular Query",
            "question": "What is AWS Lambda and how does it work?",
            "expected_mcp": False,
            "description": "Should use standard RAG endpoint"
        },
        {
            "name": "Troubleshooting Query",
            "question": "My EC2 instance i-0d0faa5b7e20d4bf1 is running slowly and has high CPU usage",
            "expected_mcp": True,
            "description": "Should trigger MCP enhancement"
        },
        {
            "name": "Performance Issue",
            "question": "I'm seeing 500 errors in my application logs, how do I troubleshoot this?",
            "expected_mcp": True,
            "description": "Should use real-time AWS data"
        },
        {
            "name": "Lambda Timeout",
            "question": "My Lambda functions are timing out and throwing errors",
            "expected_mcp": True,
            "description": "Should analyze CloudWatch metrics"
        }
    ]
    
    print("🧪 Testing Query Detection and Routing")
    print("-" * 40)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print(f"   Question: {test_case['question'][:60]}...")
        print(f"   Expected: {'MCP Enhanced' if test_case['expected_mcp'] else 'Regular RAG'}")
        
        # Test troubleshooting endpoint directly
        result = test_troubleshooting_endpoint(backend_url, test_case['question'])
        
        if result:
            mcp_enhanced = result.get('enhanced_with_mcp', False)
            aws_data = result.get('aws_real_time_data', False)
            
            if mcp_enhanced:
                print(f"   ✅ Result: MCP Enhanced with {'real-time AWS data' if aws_data else 'documentation'}")
            else:
                print(f"   ⚪ Result: Regular RAG response")
            
            # Check if behavior matches expectation
            if mcp_enhanced == test_case['expected_mcp']:
                print(f"   ✅ Behavior: As expected")
            else:
                print(f"   ⚠️  Behavior: Unexpected (got MCP={mcp_enhanced}, expected={test_case['expected_mcp']})")
        else:
            print(f"   ❌ Result: Query failed")
        
        time.sleep(1)

def test_troubleshooting_endpoint(backend_url: str, question: str):
    """Test the troubleshooting endpoint"""
    try:
        response = requests.post(
            f"{backend_url}/query/troubleshoot",
            json={
                "question": question,
                "retrieval_config": {
                    "retriever_type": "multi_vector",
                    "use_compression": True,
                    "similarity_threshold": 0.3
                }
            },
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"   ❌ HTTP {response.status_code}: {response.text[:100]}...")
            return None
            
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
        return None

def show_frontend_usage():
    """Show how to use the frontend with MCP"""
    
    print(f"\n{'='*60}")
    print("📖 How to Use MCP in the Frontend")
    print("=" * 60)
    
    print("""
🚀 **Starting the Frontend:**
   1. Make sure backend is running: python start_services.py
   2. Frontend should start automatically on http://localhost:8000
   3. Open your browser and go to the frontend URL

💬 **Using the Chat Interface:**

   Regular Questions (Standard RAG):
   • "What is AWS Lambda?"
   • "How do I configure S3 buckets?"
   • "Compare EC2 and ECS"
   
   Troubleshooting Questions (MCP Enhanced):
   • "My EC2 instance is running slowly"
   • "I'm seeing 500 errors in my logs"
   • "High CPU usage on my instances"
   • "Lambda functions are timing out"
   • "Database connections are failing"

🔍 **Visual Indicators:**
   • Regular queries show: "💡 Answer"
   • Troubleshooting queries show: "🚨 MCP-Enhanced Troubleshooting Response"
   • Real-time data indicator: "☁️ This response includes real-time AWS infrastructure data"
   • Query details show: "MCP Enhanced: ✅ YES" and "Real-time AWS Data: ✅ YES"

📋 **Commands:**
   • Type `/help` to see all available commands
   • Type `/diagrams` to see architecture diagrams
   • Type `/tokens` to see token usage

🎯 **Tips:**
   • The system automatically detects troubleshooting queries
   • No special syntax needed - just ask naturally
   • MCP responses include immediate assessment + step-by-step guidance
   • Real-time AWS data is fetched automatically for infrastructure issues
""")

def main():
    """Main function"""
    print("🧪 Frontend MCP Integration Test & Guide")
    print("=" * 60)
    
    # Test the integration
    test_frontend_integration()
    
    # Show usage guide
    show_frontend_usage()
    
    print(f"\n{'='*60}")
    print("🎉 Frontend MCP Integration Ready!")
    print("=" * 60)
    print("✅ Your frontend now supports:")
    print("   • Automatic troubleshooting query detection")
    print("   • MCP-enhanced responses with real-time AWS data")
    print("   • Visual indicators for enhanced queries")
    print("   • Seamless fallback to regular RAG")
    print("\n🔗 Frontend: http://localhost:8000")
    print("🔗 Backend: http://localhost:8888")

if __name__ == "__main__":
    main()
