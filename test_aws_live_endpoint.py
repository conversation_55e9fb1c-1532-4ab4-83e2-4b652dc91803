#!/usr/bin/env python3
"""
Test the new AWS Live endpoint (/query/aws-live)
"""

import requests
import json
import time

def test_aws_live_endpoint():
    """Test the dedicated AWS Live endpoint"""
    
    backend_url = "http://localhost:8889"
    
    test_cases = [
        {
            "name": "Inventory Query",
            "question": "How many EC2 instances do I have and what's their status?",
            "should_use_mcp": True,
            "endpoint": "/query/aws-live"
        },
        {
            "name": "Cost Query", 
            "question": "What are my current AWS costs?",
            "should_use_mcp": True,
            "endpoint": "/query/aws-live"
        },
        {
            "name": "Infrastructure Status",
            "question": "Show me my AWS infrastructure inventory",
            "should_use_mcp": True,
            "endpoint": "/query/aws-live"
        },
        {
            "name": "Performance Query",
            "question": "My EC2 instance i-0d0faa5b7e20d4bf1 is running slowly",
            "should_use_mcp": True,
            "endpoint": "/query/aws-live"
        },
        {
            "name": "Regular Query (should still work)",
            "question": "What is AWS Lambda?",
            "should_use_mcp": False,
            "endpoint": "/query/aws-live"
        }
    ]
    
    print("☁️ Testing AWS Live Endpoint (/query/aws-live)")
    print("=" * 60)
    print("🎯 This endpoint provides real-time AWS data for relevant queries")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print(f"   Question: {test_case['question']}")
        print(f"   Expected MCP: {'YES' if test_case['should_use_mcp'] else 'NO'}")
        print("-" * 50)
        
        result = make_request(f"{backend_url}{test_case['endpoint']}", test_case['question'])
        
        if result and not result.get('error'):
            mcp_enhanced = result.get('enhanced_with_mcp', False)
            aws_data = result.get('aws_real_time_data', False)
            troubleshooting_mode = result.get('troubleshooting_mode', False)
            
            print(f"   ✅ Status: SUCCESS")
            print(f"   🔧 MCP Enhanced: {'YES ✅' if mcp_enhanced else 'NO ❌'}")
            print(f"   ☁️  AWS Real-time Data: {'YES ✅' if aws_data else 'NO ❌'}")
            print(f"   🚨 Troubleshooting Mode: {'YES ✅' if troubleshooting_mode else 'NO ❌'}")
            
            # Check if behavior matches expectation
            if mcp_enhanced == test_case['should_use_mcp']:
                print(f"   ✅ Behavior: As expected")
            else:
                print(f"   ⚠️  Behavior: Unexpected (got MCP={mcp_enhanced}, expected={test_case['should_use_mcp']})")
            
            # Show response preview
            answer = result.get('answer', '')
            if answer:
                print(f"   📝 Response preview:")
                print(f"      {answer[:200]}...")
                
                # Look for real-time data indicators
                if "Real-time AWS Infrastructure Data" in answer:
                    print(f"   🔍 Contains real-time AWS data section: ✅")
                if "EC2 Instances:" in answer:
                    print(f"   📊 Contains EC2 inventory: ✅")
                if "Cost Estimates:" in answer:
                    print(f"   💰 Contains cost data: ✅")
        else:
            print(f"   ❌ Failed: {result.get('error', 'Unknown error') if result else 'No response'}")
        
        time.sleep(2)

def compare_endpoints():
    """Compare different endpoints for the same query"""
    
    backend_url = "http://localhost:8889"
    question = "How many EC2 instances do I have?"
    
    print(f"\n{'='*60}")
    print("🔄 Comparing Endpoints for Same Query")
    print("=" * 60)
    print(f"Question: {question}")
    print("=" * 60)
    
    endpoints = [
        ("/query/advanced", "Standard RAG"),
        ("/query/troubleshoot", "Troubleshooting"),
        ("/query/aws-live", "AWS Live")
    ]
    
    for endpoint, description in endpoints:
        print(f"\n📍 {description} Endpoint ({endpoint})")
        print("-" * 30)
        
        result = make_request(f"{backend_url}{endpoint}", question)
        
        if result and not result.get('error'):
            mcp_enhanced = result.get('enhanced_with_mcp', False)
            aws_data = result.get('aws_real_time_data', False)
            
            print(f"   🔧 MCP Enhanced: {'YES' if mcp_enhanced else 'NO'}")
            print(f"   ☁️  AWS Data: {'YES' if aws_data else 'NO'}")
            
            # Show key differences in response
            answer = result.get('answer', '')
            if "Real-time AWS Infrastructure Data" in answer:
                print(f"   🔍 Real-time data section: ✅")
            if "EC2 Instances:" in answer:
                print(f"   📊 Instance inventory: ✅")
        else:
            print(f"   ❌ Failed: {result.get('error', 'Unknown')}")
        
        time.sleep(1)

def make_request(url: str, question: str):
    """Make a request to the API"""
    try:
        response = requests.post(
            url,
            json={
                "question": question,
                "retrieval_config": {
                    "retriever_type": "multi_vector",
                    "use_compression": True,
                    "similarity_threshold": 0.3
                }
            },
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": f"HTTP {response.status_code}"}
            
    except Exception as e:
        return {"error": str(e)}

def main():
    """Main function"""
    test_aws_live_endpoint()
    compare_endpoints()
    
    print(f"\n{'='*60}")
    print("🎯 AWS Live Endpoint Test Complete!")
    print("=" * 60)
    print("""
✅ **Expected Results:**
   • AWS Live endpoint should provide MCP enhancement for relevant queries
   • Real-time AWS data should be included in responses
   • Responses should have clear "Real-time AWS Infrastructure Data" sections
   • Regular queries should still work but without MCP enhancement

🔗 **Frontend Integration:**
   Your frontend now automatically detects AWS-related queries and uses the AWS Live endpoint.
   
   Try these in your frontend (http://localhost:8000):
   • "How many EC2 instances do I have?"
   • "What are my AWS costs?"
   • "Show me my infrastructure status"
   
   You should see "☁️ AWS Live Response" headers with real-time data!

📊 **Endpoint Summary:**
   • /query/advanced - Standard RAG (no real-time data)
   • /query/troubleshoot - Troubleshooting focus (with MCP)
   • /query/aws-live - AWS infrastructure focus (with real-time data)
""")

if __name__ == "__main__":
    main()
