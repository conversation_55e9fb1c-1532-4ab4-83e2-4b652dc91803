#!/usr/bin/env python3
"""
Test the simplified MCP integration
"""

import requests
import json
import time

def test_simplified_mcp():
    """Test the simplified MCP integration"""
    
    backend_url = "http://localhost:8889"
    
    test_cases = [
        {
            "name": "Inventory Query",
            "question": "How many EC2 instances do I have and what's their status?",
            "should_use_mcp": True,
            "endpoint": "/query/troubleshoot"
        },
        {
            "name": "Cost Query", 
            "question": "What are my current AWS costs?",
            "should_use_mcp": True,
            "endpoint": "/query/troubleshoot"
        },
        {
            "name": "Performance Query",
            "question": "My EC2 instance i-0d0faa5b7e20d4bf1 is running slowly",
            "should_use_mcp": True,
            "endpoint": "/query/troubleshoot"
        },
        {
            "name": "Regular Query",
            "question": "What is AWS Lambda?",
            "should_use_mcp": False,
            "endpoint": "/query/troubleshoot"
        }
    ]
    
    print("🚀 Testing Simplified MCP Integration")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print(f"   Question: {test_case['question']}")
        print(f"   Expected MCP: {'YES' if test_case['should_use_mcp'] else 'NO'}")
        print("-" * 50)
        
        result = make_request(f"{backend_url}{test_case['endpoint']}", test_case['question'])
        
        if result and not result.get('error'):
            mcp_enhanced = result.get('enhanced_with_mcp', False)
            aws_data = result.get('aws_real_time_data', False)
            troubleshooting_mode = result.get('troubleshooting_mode', False)
            
            print(f"   ✅ Status: SUCCESS")
            print(f"   🔧 MCP Enhanced: {'YES ✅' if mcp_enhanced else 'NO ❌'}")
            print(f"   ☁️  AWS Real-time Data: {'YES ✅' if aws_data else 'NO ❌'}")
            print(f"   🚨 Troubleshooting Mode: {'YES ✅' if troubleshooting_mode else 'NO ❌'}")
            
            # Check if behavior matches expectation
            if mcp_enhanced == test_case['should_use_mcp']:
                print(f"   ✅ Behavior: As expected")
            else:
                print(f"   ⚠️  Behavior: Unexpected (got MCP={mcp_enhanced}, expected={test_case['should_use_mcp']})")
            
            # Show response preview
            answer = result.get('answer', '')
            if answer:
                print(f"   📝 Response preview:")
                print(f"      {answer[:200]}...")
                
                # Look for real-time data indicators
                if "Real-time AWS Data:" in answer:
                    print(f"   🔍 Contains real-time AWS data: ✅")
        else:
            print(f"   ❌ Failed: {result.get('error', 'Unknown error') if result else 'No response'}")
        
        time.sleep(2)

def make_request(url: str, question: str):
    """Make a request to the API"""
    try:
        response = requests.post(
            url,
            json={
                "question": question,
                "retrieval_config": {
                    "retriever_type": "multi_vector",
                    "use_compression": True,
                    "similarity_threshold": 0.3
                }
            },
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": f"HTTP {response.status_code}"}
            
    except Exception as e:
        return {"error": str(e)}

def main():
    """Main function"""
    test_simplified_mcp()
    
    print(f"\n{'='*60}")
    print("🎯 Simplified MCP Integration Test Complete!")
    print("=" * 60)
    print("""
✅ **Expected Results:**
   • Inventory/Cost/Performance queries should show MCP Enhanced: YES
   • Regular queries should show MCP Enhanced: NO
   • Real-time AWS data should be included in enhanced responses
   • Responses should include actual EC2 instance counts and status

🔗 **Frontend Usage:**
   Go to http://localhost:8000 and ask:
   • "How many EC2 instances do I have?"
   • "What are my AWS costs?"
   • "My instance is running slowly"
   
   You should see MCP enhancement indicators in the responses!
""")

if __name__ == "__main__":
    main()
