#!/usr/bin/env python3
"""
Quick test of enhanced MCP integration
"""

import requests
import json

def test_cost_query():
    """Test a cost-related query"""
    
    url = "http://localhost:8888/query/aws-live"
    question = "What are my AWS costs?"
    
    payload = {
        "question": question,
        "retrieval_config": {
            "retriever_type": "multi_vector",
            "use_compression": True,
            "similarity_threshold": 0.3
        }
    }
    
    print(f"🔍 Testing Enhanced MCP: {question}")
    print("=" * 50)
    
    try:
        response = requests.post(
            url,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=120
        )
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"✅ Status: SUCCESS")
            print(f"🔧 MCP Enhanced: {result.get('enhanced_with_mcp', False)}")
            print(f"☁️  AWS Real-time Data: {result.get('aws_real_time_data', False)}")
            
            answer = result.get('answer', '')
            
            # Check for enhanced features
            features = []
            if "Real-time AWS Infrastructure Data" in answer:
                features.append("Infrastructure Data")
            if "Detailed Cost Analysis" in answer:
                features.append("Cost Explorer")
            if "Current Pricing Information" in answer:
                features.append("Pricing API")
            if "30-Day Forecast" in answer:
                features.append("Cost Forecast")
            if "Total Cost" in answer:
                features.append("Cost Totals")
            
            print(f"🔍 Enhanced Features: {', '.join(features) if features else 'None'}")
            
            print(f"\n📝 Response preview:")
            print(answer[:800] + "..." if len(answer) > 800 else answer)
            
        else:
            print(f"❌ Failed: HTTP {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_cost_query()
