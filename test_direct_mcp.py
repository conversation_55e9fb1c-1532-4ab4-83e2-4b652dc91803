#!/usr/bin/env python3
"""
Direct test of MCP troubleshooting endpoint with your real AWS resources
"""

import requests
import json
import time

def test_troubleshooting_endpoint():
    """Test the troubleshooting endpoint directly"""
    
    # Your actual EC2 instance ID from the discovery
    instance_id = "i-0d0faa5b7e20d4bf1"
    
    test_cases = [
        {
            "name": "EC2 Performance Issue",
            "question": f"My EC2 instance {instance_id} is running slowly and has high CPU usage. What should I check?",
            "expected_mcp": True
        },
        {
            "name": "Application Errors",
            "question": "I'm seeing 500 errors in my application logs. How do I troubleshoot this issue?",
            "expected_mcp": True
        },
        {
            "name": "Performance Problem",
            "question": "My application performance is degraded and users are complaining about slow response times.",
            "expected_mcp": True
        },
        {
            "name": "Regular Query",
            "question": "What is AWS CloudWatch and how does it work?",
            "expected_mcp": False
        }
    ]
    
    print("🧪 Direct MCP Troubleshooting Test")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 Test {i}: {test_case['name']}")
        print(f"Question: {test_case['question']}")
        print("-" * 50)
        
        # Test with advanced endpoint (should auto-detect troubleshooting)
        result = make_request("/query/advanced", test_case['question'])
        
        if result and not result.get('error'):
            mcp_enhanced = result.get('enhanced_with_mcp', False)
            aws_data = result.get('aws_real_time_data', False)
            troubleshooting_mode = result.get('troubleshooting_mode', False)
            
            print(f"✅ Status: SUCCESS")
            print(f"🔧 MCP Enhanced: {'YES' if mcp_enhanced else 'NO'}")
            print(f"☁️  AWS Real-time Data: {'YES' if aws_data else 'NO'}")
            print(f"🚨 Troubleshooting Mode: {'YES' if troubleshooting_mode else 'NO'}")
            
            # Check if behavior matches expectation
            if mcp_enhanced == test_case['expected_mcp']:
                print(f"✅ Expected behavior: {'MCP should be used' if test_case['expected_mcp'] else 'Regular RAG should be used'}")
            else:
                print(f"⚠️  Unexpected: Expected MCP={test_case['expected_mcp']}, Got MCP={mcp_enhanced}")
            
            # Show response preview
            answer = result.get('answer', '')
            if answer:
                print(f"📝 Response preview: {answer[:150]}...")
        else:
            print(f"❌ Request failed: {result.get('error', 'Unknown error')}")
        
        time.sleep(2)

def test_mcp_server_status():
    """Check if MCP server is running properly"""
    print("\n🔧 Checking MCP Server Status")
    print("-" * 30)
    
    # Test health endpoint
    try:
        response = requests.get("http://localhost:8888/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend is running")
        else:
            print(f"⚠️  Backend health check failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Backend connection failed: {e}")
        return False
    
    return True

def make_request(endpoint: str, question: str):
    """Make a request to the specified endpoint"""
    try:
        url = f"http://localhost:8888{endpoint}"
        payload = {
            "question": question,
            "retrieval_config": {
                "retriever_type": "multi_vector",
                "use_compression": True,
                "similarity_threshold": 0.3
            }
        }
        
        response = requests.post(
            url,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": f"HTTP {response.status_code}: {response.text}"}
            
    except Exception as e:
        return {"error": str(e)}

def check_mcp_configuration():
    """Check MCP configuration"""
    print("\n🔧 Checking MCP Configuration")
    print("-" * 30)
    
    # Check if MCP is enabled in environment
    import os
    from dotenv import load_dotenv
    load_dotenv()
    
    mcp_enabled = os.getenv('MCP_ENABLED', 'false').lower() == 'true'
    aws_key = os.getenv('AWS_ACCESS_KEY_ID')
    aws_region = os.getenv('AWS_REGION')
    
    print(f"MCP_ENABLED: {mcp_enabled}")
    print(f"AWS_ACCESS_KEY_ID: {'Set' if aws_key else 'Not set'}")
    print(f"AWS_REGION: {aws_region or 'Not set'}")
    
    return mcp_enabled and aws_key and aws_region

def main():
    """Main test function"""
    print("🚀 Direct MCP Integration Test with Real AWS Resources")
    print("=" * 60)
    
    # Check configuration
    config_ok = check_mcp_configuration()
    if not config_ok:
        print("❌ MCP configuration incomplete")
        return
    
    # Check server status
    server_ok = test_mcp_server_status()
    if not server_ok:
        print("❌ Backend server not accessible")
        return
    
    # Run troubleshooting tests
    test_troubleshooting_endpoint()
    
    print(f"\n{'='*60}")
    print("🎯 Test Summary")
    print("=" * 60)
    print("If MCP enhancement is not working:")
    print("1. Check if MCP server started properly in backend logs")
    print("2. Verify AWS credentials have CloudWatch permissions")
    print("3. Check for any MCP server communication errors")
    print("\n🔗 Backend: http://localhost:8888")

if __name__ == "__main__":
    main()
