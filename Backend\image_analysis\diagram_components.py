"""
Diagram component recognition service for identifying shapes and components in diagrams.
"""
import os
import logging
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from enum import Enum
import cv2
from PIL import Image

import networkx as nx
from .models import BoundingBox
from .diagram_text import DiagramTextRecognitionService, TextElement

# Configure logging
logger = logging.getLogger(__name__)


class ComponentType(str, Enum):
    """Enum for diagram component types."""
    BOX = "box"
    CIRCLE = "circle"
    DIAMOND = "diamond"
    TRIANGLE = "triangle"
    ARROW = "arrow"
    LINE = "line"
    TEXT = "text"
    UNKNOWN = "unknown"


class DiagramComponent:
    """Model for a component in a diagram."""
    def __init__(
        self,
        component_id: str,
        component_type: ComponentType,
        bounding_box: BoundingBox,
        confidence: float,
        text: Optional[str] = None,
        properties: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize a diagram component.
        
        Args:
            component_id: Unique identifier for the component
            component_type: Type of the component
            bounding_box: Bounding box of the component
            confidence: Confidence score for the component detection
            text: Text associated with the component (optional)
            properties: Additional properties of the component (optional)
        """
        self.component_id = component_id
        self.component_type = component_type
        self.bounding_box = bounding_box
        self.confidence = confidence
        self.text = text
        self.properties = properties or {}
        self.connections = []  # List of connected component IDs
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the component to a dictionary."""
        return {
            "id": self.component_id,
            "type": self.component_type,
            "bounding_box": {
                "x": self.bounding_box.x,
                "y": self.bounding_box.y,
                "width": self.bounding_box.width,
                "height": self.bounding_box.height
            },
            "confidence": self.confidence,
            "text": self.text,
            "properties": self.properties,
            "connections": self.connections
        }


class DiagramConnection:
    """Model for a connection between components in a diagram."""
    def __init__(
        self,
        connection_id: str,
        source_id: str,
        target_id: str,
        connection_type: str,
        points: List[Tuple[float, float]],
        confidence: float,
        text: Optional[str] = None,
        properties: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize a diagram connection.
        
        Args:
            connection_id: Unique identifier for the connection
            source_id: ID of the source component
            target_id: ID of the target component
            connection_type: Type of the connection (e.g., "arrow", "line")
            points: List of points defining the connection path
            confidence: Confidence score for the connection detection
            text: Text associated with the connection (optional)
            properties: Additional properties of the connection (optional)
        """
        self.connection_id = connection_id
        self.source_id = source_id
        self.target_id = target_id
        self.connection_type = connection_type
        self.points = points
        self.confidence = confidence
        self.text = text
        self.properties = properties or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the connection to a dictionary."""
        return {
            "id": self.connection_id,
            "source_id": self.source_id,
            "target_id": self.target_id,
            "type": self.connection_type,
            "points": self.points,
            "confidence": self.confidence,
            "text": self.text,
            "properties": self.properties
        }


class DiagramComponentRecognitionService:
    """
    Service for identifying shapes and components in diagrams.
    """
    
    def __init__(self, text_recognition_service: Optional[DiagramTextRecognitionService] = None):
        """
        Initialize the diagram component recognition service.
        
        Args:
            text_recognition_service: Service for recognizing text in diagrams (optional)
        """
        self.text_recognition_service = text_recognition_service
    
    def recognize_components(self, image_path: str) -> Tuple[List[DiagramComponent], List[DiagramConnection]]:
        """
        Recognize components and their connections in a diagram.
        
        Args:
            image_path: Path to the diagram image
            
        Returns:
            Tuple containing:
            - List of diagram components
            - List of connections between components
        """
        try:
            # Read the image
            img = cv2.imread(image_path)
            if img is None:
                logger.error(f"Failed to read image: {image_path}")
                return [], []
            
            # Convert to grayscale
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # Apply Gaussian blur to reduce noise
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            
            # Extract text elements if text recognition service is available
            text_elements = []
            if self.text_recognition_service:
                text_data = self.text_recognition_service.extract_text_with_positions(image_path)
                # Extract the actual text elements from the returned dictionary
                if isinstance(text_data, dict) and "text_elements" in text_data:
                    # Convert dictionary representations back to TextElement objects
                    from .diagram_text import TextElement
                    for element_dict in text_data["text_elements"]:
                        if isinstance(element_dict, dict):
                            text_element = TextElement(
                                text=element_dict.get("text", ""),
                                bbox=tuple(element_dict.get("bbox", (0, 0, 0, 0))),
                                confidence=element_dict.get("confidence", 0.0)
                            )
                            text_elements.append(text_element)

            # Detect shapes
            components = self._detect_shapes(img, blurred)

            # Associate text with components
            self._associate_text_with_components(components, text_elements)
            
            # Detect connections between components
            connections = self._detect_connections(img, blurred, components)
            
            # Associate text with connections
            self._associate_text_with_connections(connections, text_elements)
            
            return components, connections
            
        except Exception as e:
            logger.error(f"Error recognizing diagram components: {str(e)}")
            return [], []
    
    def _detect_shapes(self, img: np.ndarray, gray: np.ndarray) -> List[DiagramComponent]:
        """
        Detect shapes in the diagram.
        
        Args:
            img: Original image
            gray: Grayscale image
            
        Returns:
            List of detected components
        """
        components = []
        
        try:
            # Apply multiple thresholding techniques for better shape detection
            # Binary threshold
            _, binary_thresh = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY_INV)
            
            # Adaptive threshold for handling varying lighting conditions
            adaptive_thresh = cv2.adaptiveThreshold(
                gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY_INV, 11, 2
            )
            
            # Combine thresholds
            combined_thresh = cv2.bitwise_or(binary_thresh, adaptive_thresh)
            
            # Apply morphological operations to clean up the image
            kernel = np.ones((3, 3), np.uint8)
            cleaned_thresh = cv2.morphologyEx(combined_thresh, cv2.MORPH_CLOSE, kernel)
            cleaned_thresh = cv2.morphologyEx(cleaned_thresh, cv2.MORPH_OPEN, kernel)
            
            # Find contours with hierarchy to detect nested shapes
            contours, hierarchy = cv2.findContours(
                cleaned_thresh, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE
            )
            
            # Filter out very small contours
            min_contour_area = (img.shape[0] * img.shape[1]) * 0.0005  # 0.05% of image area
            
            # Process each contour
            for i, contour in enumerate(contours):
                # Skip very small contours
                if cv2.contourArea(contour) < min_contour_area:
                    continue
                    
                component_id = f"component_{i}"
                
                # Get bounding box
                x, y, w, h = cv2.boundingRect(contour)
                bbox = BoundingBox(x=float(x), y=float(y), width=float(w), height=float(h))
                
                # Approximate contour to polygon
                perimeter = cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, 0.04 * perimeter, True)
                
                # Determine shape type
                component_type, confidence = self._classify_shape(contour, approx)
                
                # Extract additional properties
                properties = self._extract_shape_properties(contour, approx, component_type)
                
                # Create component
                component = DiagramComponent(
                    component_id=component_id,
                    component_type=component_type,
                    bounding_box=bbox,
                    confidence=confidence,
                    properties=properties
                )
                
                components.append(component)
            
            # Detect special components like database symbols, cloud shapes, etc.
            special_components = self._detect_special_components(img, gray)
            components.extend(special_components)
            
            return components
            
        except Exception as e:
            logger.error(f"Error detecting shapes: {str(e)}")
            return components
            
    def _extract_shape_properties(self, contour: np.ndarray, approx: np.ndarray, component_type: ComponentType) -> Dict[str, Any]:
        """
        Extract additional properties from a shape.
        
        Args:
            contour: Contour of the shape
            approx: Approximated polygon of the contour
            component_type: Type of the component
            
        Returns:
            Dict[str, Any]: Additional properties of the shape
        """
        properties = {}
        
        try:
            # Calculate basic shape metrics
            area = cv2.contourArea(contour)
            perimeter = cv2.arcLength(contour, True)
            
            properties["area"] = float(area)
            properties["perimeter"] = float(perimeter)
            
            # Calculate moments for center of mass
            M = cv2.moments(contour)
            if M["m00"] != 0:
                center_x = int(M["m10"] / M["m00"])
                center_y = int(M["m01"] / M["m00"])
                properties["center"] = (float(center_x), float(center_y))
            
            # Calculate aspect ratio for rectangles
            if component_type == ComponentType.BOX:
                x, y, w, h = cv2.boundingRect(contour)
                aspect_ratio = float(w) / h if h > 0 else 1.0
                properties["aspect_ratio"] = aspect_ratio
                
                # Determine if it's a square
                properties["is_square"] = 0.85 <= aspect_ratio <= 1.15
            
            # Calculate circularity for circles
            if component_type == ComponentType.CIRCLE:
                circularity = 4 * np.pi * area / (perimeter * perimeter) if perimeter > 0 else 0
                properties["circularity"] = float(circularity)
            
            # Calculate convexity
            hull = cv2.convexHull(contour)
            hull_area = cv2.contourArea(hull)
            convexity = area / hull_area if hull_area > 0 else 1.0
            properties["convexity"] = float(convexity)
            
            return properties
            
        except Exception as e:
            logger.error(f"Error extracting shape properties: {str(e)}")
            return properties
            
    def _detect_special_components(self, img: np.ndarray, gray: np.ndarray) -> List[DiagramComponent]:
        """
        Detect special diagram components like database symbols, cloud shapes, etc.
        
        Args:
            img: Original image
            gray: Grayscale image
            
        Returns:
            List[DiagramComponent]: List of detected special components
        """
        special_components = []
        
        try:
            # Detect database symbols (typically cylinders)
            database_components = self._detect_database_symbols(img, gray)
            special_components.extend(database_components)
            
            # Detect cloud shapes
            cloud_components = self._detect_cloud_shapes(img, gray)
            special_components.extend(cloud_components)
            
            return special_components
            
        except Exception as e:
            logger.error(f"Error detecting special components: {str(e)}")
            return special_components
            
    def _detect_database_symbols(self, img: np.ndarray, gray: np.ndarray) -> List[DiagramComponent]:
        """
        Detect database symbols (cylinders) in the diagram.
        
        Args:
            img: Original image
            gray: Grayscale image
            
        Returns:
            List[DiagramComponent]: List of detected database symbols
        """
        database_components = []
        
        try:
            # Apply edge detection
            edges = cv2.Canny(gray, 50, 150)
            
            # Apply Hough transform to detect ellipses (top and bottom of cylinder)
            # This is a simplified approach - in a real implementation, we would use more sophisticated
            # ellipse detection, but cv2.HoughEllipse is not available in all OpenCV versions
            
            # Instead, we'll use contour analysis to find potential database symbols
            # Database symbols typically have:
            # 1. An aspect ratio around 1.5-2.0 (taller than wide)
            # 2. Parallel horizontal lines at top and bottom
            # 3. Curved sides
            
            # Find contours
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Filter for potential database symbols
            for i, contour in enumerate(contours):
                # Skip very small contours
                if cv2.contourArea(contour) < 500:  # Minimum area threshold
                    continue
                
                # Get bounding box
                x, y, w, h = cv2.boundingRect(contour)
                
                # Check aspect ratio (databases are typically taller than wide)
                aspect_ratio = h / w if w > 0 else 0
                if 1.3 <= aspect_ratio <= 2.5:
                    # Check for horizontal lines at top and bottom
                    # This is a simplified check - in a real implementation, we would use more sophisticated analysis
                    
                    # Create a component for the potential database symbol
                    component_id = f"database_{i}"
                    bbox = BoundingBox(x=float(x), y=float(y), width=float(w), height=float(h))
                    
                    # Create database component with lower confidence
                    component = DiagramComponent(
                        component_id=component_id,
                        component_type=ComponentType.CIRCLE,  # Using CIRCLE as a proxy for database
                        bounding_box=bbox,
                        confidence=0.7,
                        properties={"is_database": True, "aspect_ratio": aspect_ratio}
                    )
                    
                    database_components.append(component)
            
            return database_components
            
        except Exception as e:
            logger.error(f"Error detecting database symbols: {str(e)}")
            return database_components
            
    def _detect_cloud_shapes(self, img: np.ndarray, gray: np.ndarray) -> List[DiagramComponent]:
        """
        Detect cloud shapes in the diagram.
        
        Args:
            img: Original image
            gray: Grayscale image
            
        Returns:
            List[DiagramComponent]: List of detected cloud shapes
        """
        cloud_components = []
        
        try:
            # Apply threshold
            _, thresh = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY_INV)
            
            # Find contours
            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Filter for potential cloud shapes
            for i, contour in enumerate(contours):
                # Skip very small contours
                if cv2.contourArea(contour) < 1000:  # Minimum area threshold
                    continue
                
                # Get bounding box
                x, y, w, h = cv2.boundingRect(contour)
                
                # Cloud shapes typically have:
                # 1. Low solidity (area / convex hull area)
                # 2. Many convexity defects (bumps)
                # 3. Aspect ratio close to 1.5
                
                # Calculate solidity
                hull = cv2.convexHull(contour)
                hull_area = cv2.contourArea(hull)
                solidity = cv2.contourArea(contour) / hull_area if hull_area > 0 else 0
                
                # Calculate aspect ratio
                aspect_ratio = w / h if h > 0 else 0
                
                # Check for cloud-like properties
                if (0.7 <= solidity <= 0.9) and (0.8 <= aspect_ratio <= 2.0):
                    # Count convexity defects
                    hull_indices = cv2.convexHull(contour, returnPoints=False)
                    if len(hull_indices) > 3:  # Need at least 4 points for convexity defects
                        try:
                            defects = cv2.convexityDefects(contour, hull_indices)
                            if defects is not None and len(defects) >= 4:  # Cloud shapes typically have multiple bumps
                                # Create a component for the potential cloud shape
                                component_id = f"cloud_{i}"
                                bbox = BoundingBox(x=float(x), y=float(y), width=float(w), height=float(h))
                                
                                # Create cloud component
                                component = DiagramComponent(
                                    component_id=component_id,
                                    component_type=ComponentType.UNKNOWN,  # Using UNKNOWN as a proxy for cloud
                                    bounding_box=bbox,
                                    confidence=0.7,
                                    properties={"is_cloud": True, "solidity": solidity, "defects": len(defects)}
                                )
                                
                                cloud_components.append(component)
                        except:
                            # Skip if convexity defects calculation fails
                            pass
            
            return cloud_components
            
        except Exception as e:
            logger.error(f"Error detecting cloud shapes: {str(e)}")
            return cloud_components
    
    def _classify_shape(self, contour: np.ndarray, approx: np.ndarray) -> Tuple[ComponentType, float]:
        """
        Classify a shape based on its contour.
        
        Args:
            contour: Contour of the shape
            approx: Approximated polygon of the contour
            
        Returns:
            Tuple containing:
            - ComponentType: Type of the component
            - float: Confidence score
        """
        # Count vertices
        vertices = len(approx)
        
        # Calculate shape metrics
        area = cv2.contourArea(contour)
        perimeter = cv2.arcLength(contour, True)
        
        # Calculate circularity (1.0 for perfect circle)
        circularity = 4 * np.pi * area / (perimeter * perimeter) if perimeter > 0 else 0
        
        # Classify based on vertices and shape metrics
        if vertices == 3:
            # Triangle
            return ComponentType.TRIANGLE, 0.8
        elif vertices == 4:
            # Rectangle or square
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = float(w) / h if h > 0 else 1.0
            
            # Check if it's a square (aspect ratio close to 1)
            if 0.85 <= aspect_ratio <= 1.15:
                return ComponentType.BOX, 0.9
            else:
                # Check if it's a diamond (rotated square)
                # For a diamond, the bounding box area should be significantly larger than the contour area
                bbox_area = w * h
                area_ratio = area / bbox_area if bbox_area > 0 else 0
                
                if area_ratio < 0.65:
                    return ComponentType.DIAMOND, 0.8
                else:
                    return ComponentType.BOX, 0.85
        elif vertices > 4 and vertices < 10:
            # Polygon
            return ComponentType.BOX, 0.7
        else:
            # Check if it's a circle
            if circularity > 0.7:
                return ComponentType.CIRCLE, min(circularity, 0.95)
            else:
                return ComponentType.UNKNOWN, 0.5
    
    def _detect_connections(
        self, img: np.ndarray, gray: np.ndarray, components: List[DiagramComponent]
    ) -> List[DiagramConnection]:
        """
        Detect connections between components.
        
        Args:
            img: Original image
            gray: Grayscale image
            components: List of detected components
            
        Returns:
            List of detected connections
        """
        connections = []
        
        try:
            # Apply Canny edge detection with optimized parameters
            edges = cv2.Canny(gray, 50, 150, apertureSize=3)
            
            # Apply morphological operations to clean up edges
            kernel = np.ones((2, 2), np.uint8)
            edges = cv2.dilate(edges, kernel, iterations=1)
            
            # Apply Hough Line Transform with improved parameters
            lines = cv2.HoughLinesP(
                edges, 1, np.pi/180, threshold=40, minLineLength=20, maxLineGap=15
            )
            
            if lines is None:
                return connections
            
            # Group similar lines to avoid duplicates
            grouped_lines = self._group_similar_lines(lines)
            
            # Process each line group
            for i, line_group in enumerate(grouped_lines):
                # Use the average line in the group
                avg_line = self._average_lines(line_group)
                x1, y1, x2, y2 = avg_line
                
                # Find source and target components
                source_id, target_id, source_dist, target_dist = self._find_connected_components(
                    components, x1, y1, x2, y2
                )
                
                # Only create connection if both source and target are found
                if source_id and target_id and source_id != target_id:
                    # Determine if it's an arrow or a line
                    connection_type, direction_confidence = self._determine_connection_type(
                        img, gray, x1, y1, x2, y2
                    )
                    
                    # Calculate overall confidence
                    # Higher confidence for shorter distances to components
                    distance_factor = 1.0 - min(1.0, (source_dist + target_dist) / 200.0)
                    confidence = 0.6 + (0.3 * distance_factor) + (0.1 * direction_confidence)
                    
                    # Create connection properties
                    properties = {
                        "length": np.sqrt((x2 - x1)**2 + (y2 - y1)**2),
                        "angle": np.arctan2(y2 - y1, x2 - x1) * 180 / np.pi,
                        "direction_confidence": direction_confidence
                    }
                    
                    # Create connection
                    connection = DiagramConnection(
                        connection_id=f"connection_{i}",
                        source_id=source_id,
                        target_id=target_id,
                        connection_type=connection_type,
                        points=[(float(x1), float(y1)), (float(x2), float(y2))],
                        confidence=confidence,
                        properties=properties
                    )
                    
                    connections.append(connection)
                    
                    # Update component connections
                    for component in components:
                        if component.component_id == source_id:
                            component.connections.append(target_id)
                        elif component.component_id == target_id:
                            component.connections.append(source_id)
            
            # Detect multi-segment connections
            multi_segment_connections = self._detect_multi_segment_connections(img, gray, components)
            connections.extend(multi_segment_connections)
            
            # Detect relationship types based on connection patterns
            self._identify_relationship_types(connections, components)
            
            return connections
            
        except Exception as e:
            logger.error(f"Error detecting connections: {str(e)}")
            return connections
    
    def _group_similar_lines(self, lines: np.ndarray) -> List[List[np.ndarray]]:
        """
        Group similar lines to avoid duplicate detections.
        
        Args:
            lines: Detected lines from Hough transform
            
        Returns:
            List of line groups
        """
        if lines is None or len(lines) == 0:
            return []
            
        # Initialize groups with the first line
        groups = [[lines[0][0]]]
        
        # Group similar lines
        for i in range(1, len(lines)):
            line = lines[i][0]
            x1, y1, x2, y2 = line
            
            # Check if this line is similar to any existing group
            found_group = False
            for group in groups:
                # Check similarity with the first line in the group
                gx1, gy1, gx2, gy2 = group[0]
                
                # Calculate distance between endpoints
                dist1 = np.sqrt((x1 - gx1)**2 + (y1 - gy1)**2)
                dist2 = np.sqrt((x2 - gx2)**2 + (y2 - gy2)**2)
                
                # Calculate distance between endpoints (reversed)
                dist3 = np.sqrt((x1 - gx2)**2 + (y1 - gy2)**2)
                dist4 = np.sqrt((x2 - gx1)**2 + (y2 - gy1)**2)
                
                # Check if lines are similar (close endpoints or overlapping)
                if (dist1 < 20 and dist2 < 20) or (dist3 < 20 and dist4 < 20):
                    group.append(line)
                    found_group = True
                    break
            
            # If no similar group found, create a new group
            if not found_group:
                groups.append([line])
        
        return groups
    
    def _average_lines(self, lines: List[np.ndarray]) -> np.ndarray:
        """
        Calculate the average line from a group of similar lines.
        
        Args:
            lines: Group of similar lines
            
        Returns:
            Average line [x1, y1, x2, y2]
        """
        if not lines:
            return np.array([0, 0, 0, 0])
            
        # Calculate average endpoints
        avg_x1 = sum(line[0] for line in lines) / len(lines)
        avg_y1 = sum(line[1] for line in lines) / len(lines)
        avg_x2 = sum(line[2] for line in lines) / len(lines)
        avg_y2 = sum(line[3] for line in lines) / len(lines)
        
        return np.array([int(avg_x1), int(avg_y1), int(avg_x2), int(avg_y2)])
    
    def _find_connected_components(
        self, components: List[DiagramComponent], x1: int, y1: int, x2: int, y2: int
    ) -> Tuple[Optional[str], Optional[str], float, float]:
        """
        Find components connected by a line.
        
        Args:
            components: List of diagram components
            x1, y1: First endpoint of the line
            x2, y2: Second endpoint of the line
            
        Returns:
            Tuple containing:
            - source_id: ID of the source component
            - target_id: ID of the target component
            - source_dist: Distance to source component
            - target_dist: Distance to target component
        """
        source_id = None
        target_id = None
        min_source_dist = float('inf')
        min_target_dist = float('inf')
        
        for component in components:
            # Skip non-shape components
            if component.component_type == ComponentType.LINE or component.component_type == ComponentType.ARROW:
                continue
            
            # Get component bounding box
            bbox = component.bounding_box
            
            # Check if the line endpoint is inside or near the component
            is_endpoint1_connected = self._is_point_connected_to_component(x1, y1, component)
            is_endpoint2_connected = self._is_point_connected_to_component(x2, y2, component)
            
            # Calculate distance from line endpoints to component center
            center_x = bbox.x + bbox.width / 2
            center_y = bbox.y + bbox.height / 2
            
            # Distance from first endpoint to component center
            dist1 = np.sqrt((x1 - center_x)**2 + (y1 - center_y)**2)
            
            # Distance from second endpoint to component center
            dist2 = np.sqrt((x2 - center_x)**2 + (y2 - center_y)**2)
            
            # Check if this component is closer to the first endpoint
            if is_endpoint1_connected and dist1 < min_source_dist:
                min_source_dist = dist1
                source_id = component.component_id
            
            # Check if this component is closer to the second endpoint
            if is_endpoint2_connected and dist2 < min_target_dist:
                min_target_dist = dist2
                target_id = component.component_id
        
        # If no components were found to be directly connected, use the closest ones
        if source_id is None or target_id is None:
            source_id = None
            target_id = None
            min_source_dist = float('inf')
            min_target_dist = float('inf')
            
            for component in components:
                # Skip non-shape components
                if component.component_type == ComponentType.LINE or component.component_type == ComponentType.ARROW:
                    continue
                
                # Calculate distance from line endpoints to component center
                bbox = component.bounding_box
                center_x = bbox.x + bbox.width / 2
                center_y = bbox.y + bbox.height / 2
                
                # Distance from first endpoint to component center
                dist1 = np.sqrt((x1 - center_x)**2 + (y1 - center_y)**2)
                
                # Distance from second endpoint to component center
                dist2 = np.sqrt((x2 - center_x)**2 + (y2 - center_y)**2)
                
                # Check if this component is closer to the first endpoint
                if dist1 < min_source_dist:
                    min_source_dist = dist1
                    source_id = component.component_id
                
                # Check if this component is closer to the second endpoint
                if dist2 < min_target_dist:
                    min_target_dist = dist2
                    target_id = component.component_id
        
        return source_id, target_id, min_source_dist, min_target_dist
    
    def _is_point_connected_to_component(self, x: int, y: int, component: DiagramComponent) -> bool:
        """
        Check if a point is connected to a component (inside or very close).
        
        Args:
            x, y: Point coordinates
            component: Diagram component
            
        Returns:
            bool: True if the point is connected to the component
        """
        # Get component bounding box
        bbox = component.bounding_box
        
        # Check if the point is inside the bounding box
        is_inside = (
            bbox.x <= x <= bbox.x + bbox.width and
            bbox.y <= y <= bbox.y + bbox.height
        )
        
        if is_inside:
            return True
        
        # Check if the point is very close to the bounding box
        margin = 10  # pixels
        is_close = (
            bbox.x - margin <= x <= bbox.x + bbox.width + margin and
            bbox.y - margin <= y <= bbox.y + bbox.height + margin
        )
        
        return is_close
    
    def _determine_connection_type(
        self, img: np.ndarray, gray: np.ndarray, x1: int, y1: int, x2: int, y2: int
    ) -> Tuple[str, float]:
        """
        Determine if a connection is an arrow or a line.
        
        Args:
            img: Original image
            gray: Grayscale image
            x1, y1: First endpoint of the line
            x2, y2: Second endpoint of the line
            
        Returns:
            Tuple containing:
            - connection_type: "arrow" or "line"
            - confidence: Confidence score for the determination
        """
        try:
            # Calculate line direction
            dx = x2 - x1
            dy = y2 - y1
            line_length = np.sqrt(dx**2 + dy**2)
            
            if line_length < 20:
                return "line", 0.5
            
            # Normalize direction vector
            dx /= line_length
            dy /= line_length
            
            # Check for arrowhead at the target end
            # Look at a small region around the target endpoint
            arrow_region_size = min(int(line_length * 0.2), 20)  # 20% of line length or 20 pixels max
            
            # Calculate the region to check for arrowhead
            # Extend slightly beyond the endpoint
            ext_x2 = int(x2 + dx * arrow_region_size * 0.5)
            ext_y2 = int(y2 + dy * arrow_region_size * 0.5)
            
            # Define region boundaries
            min_x = max(0, min(x2, ext_x2) - arrow_region_size)
            min_y = max(0, min(y2, ext_y2) - arrow_region_size)
            max_x = min(img.shape[1] - 1, max(x2, ext_x2) + arrow_region_size)
            max_y = min(img.shape[0] - 1, max(y2, ext_y2) + arrow_region_size)
            
            # Extract region around potential arrowhead
            if min_x >= max_x or min_y >= max_y:
                return "line", 0.5
                
            arrow_region = gray[min_y:max_y, min_x:max_x]
            
            if arrow_region.size == 0:
                return "line", 0.5
            
            # Apply edge detection to the region
            edges = cv2.Canny(arrow_region, 50, 150)
            
            # Find contours in the region
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Check for V-shaped contours (potential arrowheads)
            arrow_confidence = 0.0
            for contour in contours:
                if len(contour) < 3:
                    continue
                    
                # Approximate contour to polygon
                perimeter = cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, 0.1 * perimeter, True)
                
                # Check if it's a triangle or V-shape (3-5 points)
                if 3 <= len(approx) <= 5:
                    # Calculate contour center
                    M = cv2.moments(contour)
                    if M["m00"] != 0:
                        cx = int(M["m10"] / M["m00"]) + min_x
                        cy = int(M["m01"] / M["m00"]) + min_y
                        
                        # Check if the contour is near the endpoint
                        endpoint_dist = np.sqrt((cx - x2)**2 + (cy - y2)**2)
                        if endpoint_dist < arrow_region_size:
                            # This is likely an arrowhead
                            arrow_confidence = 0.7
                            break
            
            if arrow_confidence > 0.5:
                return "arrow", arrow_confidence
            else:
                return "line", 0.8
                
        except Exception as e:
            logger.error(f"Error determining connection type: {str(e)}")
            return "line", 0.5
    
    def _detect_multi_segment_connections(
        self, img: np.ndarray, gray: np.ndarray, components: List[DiagramComponent]
    ) -> List[DiagramConnection]:
        """
        Detect connections that consist of multiple line segments.
        
        Args:
            img: Original image
            gray: Grayscale image
            components: List of detected components
            
        Returns:
            List of detected multi-segment connections
        """
        multi_segment_connections = []
        
        try:
            # Apply edge detection
            edges = cv2.Canny(gray, 50, 150)
            
            # Apply Hough Line Transform with parameters for shorter segments
            lines = cv2.HoughLinesP(
                edges, 1, np.pi/180, threshold=30, minLineLength=15, maxLineGap=10
            )
            
            if lines is None or len(lines) < 2:
                return multi_segment_connections
            
            # Group line segments that might form a multi-segment connection
            # This is a simplified approach - in a real implementation, we would use more sophisticated
            # line segment grouping algorithms
            
            # Find potential connection endpoints (points where line segments meet)
            connection_points = []
            for i, line1 in enumerate(lines):
                x1, y1, x2, y2 = line1[0]
                
                for j, line2 in enumerate(lines):
                    if i == j:
                        continue
                        
                    x3, y3, x4, y4 = line2[0]
                    
                    # Check if endpoints are close
                    dist1 = np.sqrt((x2 - x3)**2 + (y2 - y3)**2)
                    dist2 = np.sqrt((x2 - x4)**2 + (y2 - y4)**2)
                    dist3 = np.sqrt((x1 - x3)**2 + (y1 - y3)**2)
                    dist4 = np.sqrt((x1 - x4)**2 + (y1 - y4)**2)
                    
                    min_dist = min(dist1, dist2, dist3, dist4)
                    if min_dist < 10:  # Close enough to be connected
                        # Determine which endpoints are connected
                        if dist1 == min_dist:
                            connection_points.append((i, 1, j, 0))  # line1.end -> line2.start
                        elif dist2 == min_dist:
                            connection_points.append((i, 1, j, 1))  # line1.end -> line2.end
                        elif dist3 == min_dist:
                            connection_points.append((i, 0, j, 0))  # line1.start -> line2.start
                        elif dist4 == min_dist:
                            connection_points.append((i, 0, j, 1))  # line1.start -> line2.end
            
            # Build multi-segment paths
            # This is a simplified approach that only handles simple cases
            # For a real implementation, we would use a more sophisticated path-finding algorithm
            
            # For now, just create connections for pairs of connected segments
            for i, end1, j, end2 in connection_points:
                line1 = lines[i][0]
                line2 = lines[j][0]
                
                # Determine the endpoints of the multi-segment connection
                if end1 == 0 and end2 == 0:
                    # line1.start -> line2.start, so endpoints are line1.end and line2.end
                    x1, y1 = line1[2], line1[3]
                    x2, y2 = line2[2], line2[3]
                    middle_x, middle_y = line1[0], line1[1]  # or line2[0], line2[1]
                elif end1 == 0 and end2 == 1:
                    # line1.start -> line2.end, so endpoints are line1.end and line2.start
                    x1, y1 = line1[2], line1[3]
                    x2, y2 = line2[0], line2[1]
                    middle_x, middle_y = line1[0], line1[1]  # or line2[2], line2[3]
                elif end1 == 1 and end2 == 0:
                    # line1.end -> line2.start, so endpoints are line1.start and line2.end
                    x1, y1 = line1[0], line1[1]
                    x2, y2 = line2[2], line2[3]
                    middle_x, middle_y = line1[2], line1[3]  # or line2[0], line2[1]
                else:  # end1 == 1 and end2 == 1
                    # line1.end -> line2.end, so endpoints are line1.start and line2.start
                    x1, y1 = line1[0], line1[1]
                    x2, y2 = line2[0], line2[1]
                    middle_x, middle_y = line1[2], line1[3]  # or line2[2], line2[3]
                
                # Find source and target components
                source_id, target_id, source_dist, target_dist = self._find_connected_components(
                    components, x1, y1, x2, y2
                )
                
                # Only create connection if both source and target are found
                if source_id and target_id and source_id != target_id:
                    # Determine if it's an arrow or a line
                    connection_type, direction_confidence = self._determine_connection_type(
                        img, gray, x1, y1, x2, y2
                    )
                    
                    # Calculate overall confidence
                    confidence = 0.6 + (0.1 * direction_confidence)
                    
                    # Create connection with multiple points
                    connection = DiagramConnection(
                        connection_id=f"multi_connection_{i}_{j}",
                        source_id=source_id,
                        target_id=target_id,
                        connection_type=connection_type,
                        points=[(float(x1), float(y1)), (float(middle_x), float(middle_y)), (float(x2), float(y2))],
                        confidence=confidence,
                        properties={"is_multi_segment": True}
                    )
                    
                    multi_segment_connections.append(connection)
                    
                    # Update component connections
                    for component in components:
                        if component.component_id == source_id:
                            component.connections.append(target_id)
                        elif component.component_id == target_id:
                            component.connections.append(source_id)
            
            return multi_segment_connections
            
        except Exception as e:
            logger.error(f"Error detecting multi-segment connections: {str(e)}")
            return multi_segment_connections
    
    def _identify_relationship_types(
        self, connections: List[DiagramConnection], components: List[DiagramComponent]
    ) -> None:
        """
        Identify relationship types based on connection patterns.
        
        Args:
            connections: List of detected connections
            components: List of detected components
        """
        try:
            # Create a dictionary of components by ID for quick lookup
            component_dict = {component.component_id: component for component in components}
            
            # Analyze each connection
            for connection in connections:
                # Skip if source or target is missing
                if connection.source_id not in component_dict or connection.target_id not in component_dict:
                    continue
                
                source = component_dict[connection.source_id]
                target = component_dict[connection.target_id]
                
                # Determine relationship type based on component types
                relationship_type = "generic"
                
                # Check for database relationships
                if "is_database" in source.properties and source.properties.get("is_database", False):
                    relationship_type = "database_access"
                elif "is_database" in target.properties and target.properties.get("is_database", False):
                    relationship_type = "database_access"
                
                # Check for cloud relationships
                elif "is_cloud" in source.properties and source.properties.get("is_cloud", False):
                    relationship_type = "cloud_service"
                elif "is_cloud" in target.properties and target.properties.get("is_cloud", False):
                    relationship_type = "cloud_service"
                
                # Check for hierarchical relationships
                elif source.component_type == target.component_type:
                    relationship_type = "peer"
                elif source.component_type == ComponentType.BOX and target.component_type != ComponentType.BOX:
                    relationship_type = "container"
                elif source.component_type != ComponentType.BOX and target.component_type == ComponentType.BOX:
                    relationship_type = "contained_by"
                
                # Store the relationship type
                connection.properties["relationship_type"] = relationship_type
                
                # Determine directionality
                if connection.connection_type == "arrow":
                    connection.properties["directional"] = True
                else:
                    connection.properties["directional"] = False
                
        except Exception as e:
            logger.error(f"Error identifying relationship types: {str(e)}")
            return
    
    def _associate_text_with_components(
        self, components: List[DiagramComponent], text_elements: List[TextElement]
    ) -> None:
        """
        Associate text elements with components.
        
        Args:
            components: List of detected components
            text_elements: List of text elements
        """
        if not text_elements:
            return
        
        for text_element in text_elements:
            # Skip empty text
            if not text_element.text.strip():
                continue
            
            # Find the closest component
            closest_component = None
            min_distance = float('inf')
            
            for component in components:
                # Skip text components
                if component.component_type == ComponentType.TEXT:
                    continue
                
                # Calculate distance between text and component centers
                text_center_x = text_element.bbox.x + text_element.bbox.width / 2
                text_center_y = text_element.bbox.y + text_element.bbox.height / 2
                
                component_center_x = component.bounding_box.x + component.bounding_box.width / 2
                component_center_y = component.bounding_box.y + component.bounding_box.height / 2
                
                distance = np.sqrt(
                    (text_center_x - component_center_x)**2 + 
                    (text_center_y - component_center_y)**2
                )
                
                # Check if this component is closer
                if distance < min_distance:
                    min_distance = distance
                    closest_component = component
            
            # Associate text with the closest component if it's close enough
            if closest_component and min_distance < max(
                closest_component.bounding_box.width, 
                closest_component.bounding_box.height
            ) * 1.5:
                closest_component.text = text_element.text
    
    def _associate_text_with_connections(
        self, connections: List[DiagramConnection], text_elements: List[TextElement]
    ) -> None:
        """
        Associate text elements with connections.
        
        Args:
            connections: List of detected connections
            text_elements: List of text elements
        """
        if not text_elements or not connections:
            return
        
        for text_element in text_elements:
            # Skip empty text
            if not text_element.text.strip():
                continue
            
            # Find the closest connection
            closest_connection = None
            min_distance = float('inf')
            
            for connection in connections:
                # Calculate distance from text center to line
                text_center_x = text_element.bbox.x + text_element.bbox.width / 2
                text_center_y = text_element.bbox.y + text_element.bbox.height / 2
                
                # Calculate distance to each line segment
                for i in range(len(connection.points) - 1):
                    x1, y1 = connection.points[i]
                    x2, y2 = connection.points[i + 1]
                    
                    # Calculate distance from point to line segment
                    distance = self._point_to_line_distance(
                        text_center_x, text_center_y, x1, y1, x2, y2
                    )
                    
                    if distance < min_distance:
                        min_distance = distance
                        closest_connection = connection
            
            # Associate text with the closest connection if it's close enough
            if closest_connection and min_distance < 50:  # Threshold distance in pixels
                closest_connection.text = text_element.text
    
    def _point_to_line_distance(
        self, px: float, py: float, x1: float, y1: float, x2: float, y2: float
    ) -> float:
        """
        Calculate the distance from a point to a line segment.
        
        Args:
            px, py: Point coordinates
            x1, y1: First endpoint of the line segment
            x2, y2: Second endpoint of the line segment
            
        Returns:
            float: Distance from the point to the line segment
        """
        # Calculate line length squared
        line_length_sq = (x2 - x1)**2 + (y2 - y1)**2
        
        if line_length_sq == 0:
            # Line segment is actually a point
            return np.sqrt((px - x1)**2 + (py - y1)**2)
        
        # Calculate projection of point onto line
        t = max(0, min(1, ((px - x1) * (x2 - x1) + (py - y1) * (y2 - y1)) / line_length_sq))
        
        # Calculate closest point on line segment
        proj_x = x1 + t * (x2 - x1)
        proj_y = y1 + t * (y2 - y1)
        
        # Calculate distance from point to closest point on line segment
        return np.sqrt((px - proj_x)**2 + (py - proj_y)**2)
    
    def get_diagram_structure(self, image_path: str) -> Dict[str, Any]:
        """
        Get the structure of a diagram.
        
        Args:
            image_path: Path to the diagram image
            
        Returns:
            Dict[str, Any]: Structure of the diagram
        """
        components, connections = self.recognize_components(image_path)
        
        return {
            "components": [component.to_dict() for component in components],
            "connections": [connection.to_dict() for connection in connections]
        }

    def create_diagram_graph(self, components: List[DiagramComponent], connections: List[DiagramConnection]) -> nx.DiGraph:
        """
        Create a networkx graph from the diagram components and connections.
        
        Args:
            components: List of diagram components
            connections: List of connections between components
            
        Returns:
            nx.DiGraph: A directed graph representing the diagram
        """
        G = nx.DiGraph()
        
        # Add nodes to the graph
        for component in components:
            G.add_node(component.component_id, **component.to_dict())
            
        # Add edges to the graph
        for connection in connections:
            if G.has_node(connection.source_id) and G.has_node(connection.target_id):
                G.add_edge(connection.source_id, connection.target_id, **connection.to_dict())
                
        return G