#!/usr/bin/env python3
"""
Debug MCP integration issues
"""

import requests
import json
import time

def test_mcp_endpoints():
    """Test different endpoints to see where MCP fails"""
    
    backend_url = "http://localhost:8889"
    
    test_question = "How many EC2 instances do I have and what's their status?"
    
    print("🔍 Debugging MCP Integration Issues")
    print("=" * 60)
    
    # Test 1: Health check
    print("\n1. Testing Backend Health")
    try:
        response = requests.get(f"{backend_url}/health", timeout=5)
        if response.status_code == 200:
            print("   ✅ Backend is healthy")
        else:
            print(f"   ❌ Backend health check failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Backend connection failed: {e}")
        return
    
    # Test 2: Regular advanced endpoint
    print("\n2. Testing Regular Advanced Endpoint")
    result = test_endpoint(f"{backend_url}/query/advanced", test_question)
    if result:
        print(f"   MCP Enhanced: {result.get('enhanced_with_mcp', False)}")
        print(f"   AWS Data: {result.get('aws_real_time_data', False)}")
    
    # Test 3: Troubleshooting endpoint
    print("\n3. Testing Troubleshooting Endpoint")
    result = test_endpoint(f"{backend_url}/query/troubleshoot", test_question)
    if result:
        print(f"   MCP Enhanced: {result.get('enhanced_with_mcp', False)}")
        print(f"   AWS Data: {result.get('aws_real_time_data', False)}")
    
    # Test 4: Different question types
    print("\n4. Testing Different Question Types")
    
    questions = [
        ("Inventory", "How many instances do I have?"),
        ("Cost", "What are my AWS costs?"),
        ("Troubleshooting", "My EC2 instance is slow"),
        ("Regular", "What is AWS Lambda?")
    ]
    
    for category, question in questions:
        print(f"\n   {category}: {question}")
        result = test_endpoint(f"{backend_url}/query/troubleshoot", question)
        if result:
            mcp = result.get('enhanced_with_mcp', False)
            print(f"      MCP: {'✅' if mcp else '❌'}")
        else:
            print(f"      ❌ Failed")

def test_endpoint(url: str, question: str):
    """Test a specific endpoint"""
    try:
        response = requests.post(
            url,
            json={
                "question": question,
                "retrieval_config": {
                    "retriever_type": "multi_vector",
                    "use_compression": True,
                    "similarity_threshold": 0.3
                }
            },
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"   ❌ HTTP {response.status_code}")
            return None
            
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
        return None

def check_backend_logs():
    """Instructions for checking backend logs"""
    print(f"\n{'='*60}")
    print("🔧 Debugging Steps")
    print("=" * 60)
    print("""
1. **Check Backend Logs**:
   Look at the terminal where you started the backend for:
   - "MCP enhanced query engine initialized successfully"
   - "MCP AWS server started successfully"
   - Any MCP-related errors

2. **Check MCP Server Status**:
   Look for these log messages:
   - "AWS clients initialized successfully"
   - "MCP AWS server started successfully"
   - Any AWS authentication errors

3. **Common Issues**:
   - MCP server not starting due to missing dependencies
   - AWS credentials not properly configured
   - MCP process communication errors
   - Query detection logic not triggering

4. **Manual Test**:
   Try asking in the frontend: "My EC2 instance is slow"
   This should definitely trigger MCP enhancement.

5. **Check Environment**:
   Ensure these are set in .env:
   - MCP_ENABLED=true
   - AWS_ACCESS_KEY_ID=...
   - AWS_SECRET_ACCESS_KEY=...
   - AWS_REGION=ap-south-1
""")

def suggest_fixes():
    """Suggest potential fixes"""
    print(f"\n{'='*60}")
    print("🔧 Potential Fixes")
    print("=" * 60)
    print("""
**If MCP is not triggering:**

1. **Restart Backend with Verbose Logging**:
   - Stop current backend (Ctrl+C)
   - Start with: python Backend/main.py
   - Watch for MCP initialization messages

2. **Test MCP Server Directly**:
   - Run: python Backend/test_mcp_integration.py
   - This will test MCP server independently

3. **Check Query Detection**:
   - Try exact phrases: "My EC2 instance is slow"
   - Try inventory phrases: "How many instances do I have"
   - Try cost phrases: "What are my AWS costs"

4. **Force MCP Endpoint**:
   - Use /query/troubleshoot endpoint directly
   - This should always try to use MCP

5. **Check Dependencies**:
   - Ensure mcp package is installed
   - Check if all AWS clients are working
""")

def main():
    """Main debugging function"""
    test_mcp_endpoints()
    check_backend_logs()
    suggest_fixes()
    
    print(f"\n{'='*60}")
    print("🎯 Next Steps")
    print("=" * 60)
    print("""
1. Check the backend terminal for MCP initialization messages
2. Try the exact question: "My EC2 instance is slow" in frontend
3. If still not working, restart backend and watch logs
4. Run the MCP integration test: python Backend/test_mcp_integration.py
""")

if __name__ == "__main__":
    main()
