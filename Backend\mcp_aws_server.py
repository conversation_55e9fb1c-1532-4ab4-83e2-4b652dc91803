"""
MCP Server for AWS Production Troubleshooting Integration
Provides read-only access to AWS services for production issue diagnosis
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import boto3
from botocore.exceptions import ClientError, NoCredentialsError
import os
from dotenv import load_dotenv

# MCP imports
try:
    from mcp.server import Server
    from mcp.server.models import InitializationOptions
    from mcp.server.stdio import stdio_server
    from mcp.types import (
        Resource, Tool, TextContent, ImageContent, EmbeddedResource,
        CallToolRequest, CallToolResult, ListResourcesRequest, ListResourcesResult,
        ListToolsRequest, ListToolsResult, ReadResourceRequest, ReadResourceResult
    )
except ImportError:
    print("MCP library not installed. Install with: pip install mcp")
    raise

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AWSMCPServer:
    """MCP Server for AWS troubleshooting operations"""
    
    def __init__(self):
        self.server = Server("aws-troubleshooting")
        self.aws_session = None
        self._setup_aws_clients()
        self._register_tools()
        self._register_resources()
    
    def _setup_aws_clients(self):
        """Initialize AWS clients with read-only permissions"""
        try:
            # Use existing AWS credentials from environment
            self.aws_session = boto3.Session(
                aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
                aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
                region_name=os.getenv("AWS_REGION", "us-east-1")
            )
            
            # Initialize AWS service clients
            self.cloudwatch = self.aws_session.client('cloudwatch')
            self.logs = self.aws_session.client('logs')
            self.ec2 = self.aws_session.client('ec2')
            self.ecs = self.aws_session.client('ecs')
            self.rds = self.aws_session.client('rds')
            self.lambda_client = self.aws_session.client('lambda')
            self.application_insights = self.aws_session.client('application-insights')
            
            logger.info("AWS clients initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize AWS clients: {e}")
            raise
    
    def _register_tools(self):
        """Register MCP tools for AWS operations"""
        
        @self.server.call_tool()
        async def get_cloudwatch_metrics(arguments: Dict[str, Any]) -> CallToolResult:
            """Get CloudWatch metrics for a specific service/resource"""
            try:
                namespace = arguments.get("namespace", "AWS/EC2")
                metric_name = arguments.get("metric_name", "CPUUtilization")
                dimensions = arguments.get("dimensions", [])
                start_time = arguments.get("start_time")
                end_time = arguments.get("end_time")
                
                # Default to last 1 hour if no time specified
                if not start_time:
                    start_time = datetime.utcnow() - timedelta(hours=1)
                if not end_time:
                    end_time = datetime.utcnow()
                
                response = self.cloudwatch.get_metric_statistics(
                    Namespace=namespace,
                    MetricName=metric_name,
                    Dimensions=dimensions,
                    StartTime=start_time,
                    EndTime=end_time,
                    Period=300,  # 5 minutes
                    Statistics=['Average', 'Maximum', 'Minimum']
                )
                
                return CallToolResult(
                    content=[TextContent(
                        type="text",
                        text=json.dumps(response['Datapoints'], indent=2, default=str)
                    )]
                )
            except Exception as e:
                return CallToolResult(
                    content=[TextContent(type="text", text=f"Error getting metrics: {str(e)}")],
                    isError=True
                )
        
        @self.server.call_tool()
        async def query_cloudwatch_logs(arguments: Dict[str, Any]) -> CallToolResult:
            """Query CloudWatch logs for error patterns"""
            try:
                log_group = arguments.get("log_group")
                query_string = arguments.get("query", "ERROR")
                start_time = arguments.get("start_time")
                end_time = arguments.get("end_time")
                
                if not log_group:
                    return CallToolResult(
                        content=[TextContent(type="text", text="log_group parameter is required")],
                        isError=True
                    )
                
                # Default to last 1 hour
                if not start_time:
                    start_time = int((datetime.utcnow() - timedelta(hours=1)).timestamp())
                if not end_time:
                    end_time = int(datetime.utcnow().timestamp())
                
                # Start CloudWatch Insights query
                response = self.logs.start_query(
                    logGroupName=log_group,
                    startTime=start_time,
                    endTime=end_time,
                    queryString=f'fields @timestamp, @message | filter @message like /{query_string}/ | sort @timestamp desc | limit 100'
                )
                
                query_id = response['queryId']
                
                # Wait for query to complete
                while True:
                    result = self.logs.get_query_results(queryId=query_id)
                    if result['status'] == 'Complete':
                        break
                    elif result['status'] == 'Failed':
                        return CallToolResult(
                            content=[TextContent(type="text", text="Query failed")],
                            isError=True
                        )
                    await asyncio.sleep(1)
                
                return CallToolResult(
                    content=[TextContent(
                        type="text",
                        text=json.dumps(result['results'], indent=2)
                    )]
                )
            except Exception as e:
                return CallToolResult(
                    content=[TextContent(type="text", text=f"Error querying logs: {str(e)}")],
                    isError=True
                )
        
        @self.server.call_tool()
        async def get_ec2_instance_health(arguments: Dict[str, Any]) -> CallToolResult:
            """Get EC2 instance health and status"""
            try:
                instance_ids = arguments.get("instance_ids", [])
                
                if instance_ids:
                    response = self.ec2.describe_instances(InstanceIds=instance_ids)
                else:
                    response = self.ec2.describe_instances()
                
                instances = []
                for reservation in response['Reservations']:
                    for instance in reservation['Instances']:
                        instances.append({
                            'InstanceId': instance['InstanceId'],
                            'State': instance['State']['Name'],
                            'InstanceType': instance['InstanceType'],
                            'LaunchTime': instance['LaunchTime'],
                            'PrivateIpAddress': instance.get('PrivateIpAddress'),
                            'PublicIpAddress': instance.get('PublicIpAddress'),
                            'Tags': instance.get('Tags', [])
                        })
                
                return CallToolResult(
                    content=[TextContent(
                        type="text",
                        text=json.dumps(instances, indent=2, default=str)
                    )]
                )
            except Exception as e:
                return CallToolResult(
                    content=[TextContent(type="text", text=f"Error getting EC2 health: {str(e)}")],
                    isError=True
                )
        
        @self.server.call_tool()
        async def get_rds_instance_status(arguments: Dict[str, Any]) -> CallToolResult:
            """Get RDS instance status and health"""
            try:
                db_instance_identifier = arguments.get("db_instance_identifier")
                
                if db_instance_identifier:
                    response = self.rds.describe_db_instances(
                        DBInstanceIdentifier=db_instance_identifier
                    )
                else:
                    response = self.rds.describe_db_instances()
                
                instances = []
                for db_instance in response['DBInstances']:
                    instances.append({
                        'DBInstanceIdentifier': db_instance['DBInstanceIdentifier'],
                        'DBInstanceStatus': db_instance['DBInstanceStatus'],
                        'Engine': db_instance['Engine'],
                        'EngineVersion': db_instance['EngineVersion'],
                        'DBInstanceClass': db_instance['DBInstanceClass'],
                        'AllocatedStorage': db_instance['AllocatedStorage'],
                        'AvailabilityZone': db_instance['AvailabilityZone'],
                        'MultiAZ': db_instance['MultiAZ'],
                        'Endpoint': db_instance.get('Endpoint', {}).get('Address')
                    })
                
                return CallToolResult(
                    content=[TextContent(
                        type="text",
                        text=json.dumps(instances, indent=2, default=str)
                    )]
                )
            except Exception as e:
                return CallToolResult(
                    content=[TextContent(type="text", text=f"Error getting RDS status: {str(e)}")],
                    isError=True
                )
        
        @self.server.call_tool()
        async def analyze_error_patterns(arguments: Dict[str, Any]) -> CallToolResult:
            """Analyze error patterns across multiple AWS services"""
            try:
                services = arguments.get("services", ["ec2", "rds", "lambda"])
                time_range_hours = arguments.get("time_range_hours", 1)
                
                analysis_results = {}
                
                # Analyze CloudWatch alarms
                alarms_response = self.cloudwatch.describe_alarms(
                    StateValue='ALARM',
                    MaxRecords=100
                )
                
                analysis_results['active_alarms'] = [
                    {
                        'AlarmName': alarm['AlarmName'],
                        'StateReason': alarm['StateReason'],
                        'StateUpdatedTimestamp': alarm['StateUpdatedTimestamp'],
                        'MetricName': alarm['MetricName'],
                        'Namespace': alarm['Namespace']
                    }
                    for alarm in alarms_response['MetricAlarms']
                ]
                
                return CallToolResult(
                    content=[TextContent(
                        type="text",
                        text=json.dumps(analysis_results, indent=2, default=str)
                    )]
                )
            except Exception as e:
                return CallToolResult(
                    content=[TextContent(type="text", text=f"Error analyzing patterns: {str(e)}")],
                    isError=True
                )

        @self.server.call_tool()
        async def get_aws_inventory(arguments: Dict[str, Any]) -> CallToolResult:
            """Get comprehensive AWS resource inventory"""
            try:
                inventory = {}

                # Get EC2 instances
                ec2_response = self.ec2.describe_instances()
                instances = []
                for reservation in ec2_response['Reservations']:
                    for instance in reservation['Instances']:
                        if instance['State']['Name'] != 'terminated':
                            instances.append({
                                'InstanceId': instance['InstanceId'],
                                'InstanceType': instance['InstanceType'],
                                'State': instance['State']['Name'],
                                'LaunchTime': instance.get('LaunchTime'),
                                'Platform': instance.get('Platform', 'Linux'),
                                'PrivateIpAddress': instance.get('PrivateIpAddress'),
                                'PublicIpAddress': instance.get('PublicIpAddress')
                            })
                inventory['EC2'] = {
                    'count': len(instances),
                    'instances': instances
                }

                # Get Lambda functions
                lambda_response = self.lambda_client.list_functions()
                functions = []
                for func in lambda_response['Functions']:
                    functions.append({
                        'FunctionName': func['FunctionName'],
                        'Runtime': func['Runtime'],
                        'LastModified': func['LastModified'],
                        'CodeSize': func['CodeSize'],
                        'MemorySize': func['MemorySize'],
                        'Timeout': func['Timeout']
                    })
                inventory['Lambda'] = {
                    'count': len(functions),
                    'functions': functions
                }

                # Get S3 buckets
                s3_response = self.s3.list_buckets()
                buckets = [bucket['Name'] for bucket in s3_response['Buckets']]
                inventory['S3'] = {
                    'count': len(buckets),
                    'buckets': buckets
                }

                # Get RDS instances
                try:
                    rds_response = self.rds.describe_db_instances()
                    db_instances = []
                    for db in rds_response['DBInstances']:
                        db_instances.append({
                            'DBInstanceIdentifier': db['DBInstanceIdentifier'],
                            'DBInstanceClass': db['DBInstanceClass'],
                            'DBInstanceStatus': db['DBInstanceStatus'],
                            'Engine': db['Engine'],
                            'EngineVersion': db['EngineVersion']
                        })
                    inventory['RDS'] = {
                        'count': len(db_instances),
                        'instances': db_instances
                    }
                except Exception as e:
                    inventory['RDS'] = {
                        'count': 0,
                        'instances': [],
                        'error': str(e)
                    }

                return CallToolResult(
                    content=[TextContent(
                        type="text",
                        text=json.dumps(inventory, indent=2, default=str)
                    )]
                )
            except Exception as e:
                return CallToolResult(
                    content=[TextContent(type="text", text=f"Error getting inventory: {str(e)}")],
                    isError=True
                )

        @self.server.call_tool()
        async def get_aws_costs(arguments: Dict[str, Any]) -> CallToolResult:
            """Get AWS cost and billing information"""
            try:
                # Note: This requires Cost Explorer API access
                # For now, we'll provide cost estimation based on resources
                cost_info = {
                    "note": "Real-time cost data requires AWS Cost Explorer API access",
                    "resource_based_estimates": {}
                }

                # Get EC2 instance costs estimation
                ec2_response = self.ec2.describe_instances()
                running_instances = []
                for reservation in ec2_response['Reservations']:
                    for instance in reservation['Instances']:
                        if instance['State']['Name'] == 'running':
                            running_instances.append({
                                'InstanceId': instance['InstanceId'],
                                'InstanceType': instance['InstanceType'],
                                'LaunchTime': instance.get('LaunchTime')
                            })

                cost_info['resource_based_estimates']['EC2'] = {
                    'running_instances': len(running_instances),
                    'instances': running_instances,
                    'note': 'Costs depend on instance types, usage hours, and region'
                }

                # Get Lambda function info for cost estimation
                lambda_response = self.lambda_client.list_functions()
                cost_info['resource_based_estimates']['Lambda'] = {
                    'function_count': len(lambda_response['Functions']),
                    'note': 'Costs based on invocations, duration, and memory allocation'
                }

                # Get S3 bucket info
                s3_response = self.s3.list_buckets()
                cost_info['resource_based_estimates']['S3'] = {
                    'bucket_count': len(s3_response['Buckets']),
                    'note': 'Costs based on storage usage, requests, and data transfer'
                }

                return CallToolResult(
                    content=[TextContent(
                        type="text",
                        text=json.dumps(cost_info, indent=2, default=str)
                    )]
                )
            except Exception as e:
                return CallToolResult(
                    content=[TextContent(type="text", text=f"Error getting cost info: {str(e)}")],
                    isError=True
                )

    def _register_resources(self):
        """Register MCP resources for AWS data"""
        
        @self.server.list_resources()
        async def list_resources() -> ListResourcesResult:
            """List available AWS resources for troubleshooting"""
            return ListResourcesResult(
                resources=[
                    Resource(
                        uri="aws://cloudwatch/metrics",
                        name="CloudWatch Metrics",
                        description="Access to CloudWatch metrics for performance monitoring",
                        mimeType="application/json"
                    ),
                    Resource(
                        uri="aws://cloudwatch/logs",
                        name="CloudWatch Logs",
                        description="Access to CloudWatch logs for error analysis",
                        mimeType="application/json"
                    ),
                    Resource(
                        uri="aws://ec2/instances",
                        name="EC2 Instances",
                        description="EC2 instance health and status information",
                        mimeType="application/json"
                    ),
                    Resource(
                        uri="aws://rds/instances",
                        name="RDS Instances",
                        description="RDS database instance status and configuration",
                        mimeType="application/json"
                    )
                ]
            )
    
    async def run(self):
        """Run the MCP server"""
        async with stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="aws-troubleshooting",
                    server_version="1.0.0",
                    capabilities=self.server.get_capabilities(
                        notification_options=None,
                        experimental_capabilities=None
                    )
                )
            )

if __name__ == "__main__":
    server = AWSMCPServer()
    asyncio.run(server.run())
