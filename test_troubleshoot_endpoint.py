#!/usr/bin/env python3
"""
Test the dedicated troubleshooting endpoint with real AWS resources
"""

import requests
import json
import time

def test_troubleshooting_endpoint():
    """Test the dedicated /query/troubleshoot endpoint"""
    
    # Your actual EC2 instance ID
    instance_id = "i-0d0faa5b7e20d4bf1"
    
    test_cases = [
        {
            "name": "Real EC2 Performance Issue",
            "question": f"My EC2 instance {instance_id} is running slowly and has high CPU usage. What real-time metrics should I check and how do I troubleshoot this?",
            "endpoint": "/query/troubleshoot"
        },
        {
            "name": "Application 500 Errors",
            "question": "I'm seeing 500 errors in my application logs. Can you check my AWS infrastructure and help me troubleshoot this issue?",
            "endpoint": "/query/troubleshoot"
        },
        {
            "name": "Performance Degradation",
            "question": "My application performance is degraded. Please analyze my AWS metrics and provide troubleshooting steps.",
            "endpoint": "/query/troubleshoot"
        },
        {
            "name": "Lambda Function Issues",
            "question": "My Lambda functions are timing out and throwing errors. What should I check in CloudWatch?",
            "endpoint": "/query/troubleshoot"
        }
    ]
    
    print("🚨 Testing Dedicated Troubleshooting Endpoint")
    print("=" * 60)
    print("🎯 This should trigger MCP enhancement with real-time AWS data")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 Test {i}: {test_case['name']}")
        print(f"Endpoint: {test_case['endpoint']}")
        print(f"Question: {test_case['question']}")
        print("-" * 50)
        
        result = make_request(test_case['endpoint'], test_case['question'])
        
        if result and not result.get('error'):
            mcp_enhanced = result.get('enhanced_with_mcp', False)
            aws_data = result.get('aws_real_time_data', False)
            troubleshooting_mode = result.get('troubleshooting_mode', False)
            
            print(f"✅ Status: SUCCESS")
            print(f"🔧 MCP Enhanced: {'YES ✅' if mcp_enhanced else 'NO ❌'}")
            print(f"☁️  AWS Real-time Data: {'YES ✅' if aws_data else 'NO ❌'}")
            print(f"🚨 Troubleshooting Mode: {'YES ✅' if troubleshooting_mode else 'NO ❌'}")
            
            # Show response preview
            answer = result.get('answer', '')
            if answer:
                print(f"\n📝 Response preview:")
                print(f"{answer[:300]}...")
                
                # Look for real-time data indicators in the response
                real_time_indicators = [
                    "real-time", "current", "CloudWatch metrics", "immediate assessment",
                    "based on the observed", "live data", "current status"
                ]
                
                found_indicators = [ind for ind in real_time_indicators if ind.lower() in answer.lower()]
                if found_indicators:
                    print(f"\n🔍 Real-time indicators found: {', '.join(found_indicators)}")
            
            # Show sources
            sources = result.get('sources', [])
            if sources:
                print(f"\n📚 Sources: {len(sources)} documents")
        else:
            print(f"❌ Request failed: {result.get('error', 'Unknown error')}")
        
        time.sleep(3)

def compare_endpoints():
    """Compare regular vs troubleshooting endpoints"""
    
    question = "My EC2 instance i-0d0faa5b7e20d4bf1 has high CPU usage. What should I check?"
    
    print(f"\n{'='*60}")
    print("🔄 Comparing Regular vs Troubleshooting Endpoints")
    print("=" * 60)
    print(f"Question: {question}")
    print("=" * 60)
    
    # Test regular endpoint
    print("\n1️⃣ Regular Endpoint (/query/advanced)")
    print("-" * 30)
    regular_result = make_request("/query/advanced", question)
    
    if regular_result and not regular_result.get('error'):
        print(f"🔧 MCP Enhanced: {'YES' if regular_result.get('enhanced_with_mcp') else 'NO'}")
        print(f"☁️  AWS Data: {'YES' if regular_result.get('aws_real_time_data') else 'NO'}")
    else:
        print(f"❌ Failed: {regular_result.get('error', 'Unknown')}")
    
    time.sleep(2)
    
    # Test troubleshooting endpoint
    print("\n2️⃣ Troubleshooting Endpoint (/query/troubleshoot)")
    print("-" * 30)
    troubleshoot_result = make_request("/query/troubleshoot", question)
    
    if troubleshoot_result and not troubleshoot_result.get('error'):
        print(f"🔧 MCP Enhanced: {'YES' if troubleshoot_result.get('enhanced_with_mcp') else 'NO'}")
        print(f"☁️  AWS Data: {'YES' if troubleshoot_result.get('aws_real_time_data') else 'NO'}")
    else:
        print(f"❌ Failed: {troubleshoot_result.get('error', 'Unknown')}")

def make_request(endpoint: str, question: str):
    """Make a request to the specified endpoint"""
    try:
        url = f"http://localhost:8888{endpoint}"
        payload = {
            "question": question,
            "retrieval_config": {
                "retriever_type": "multi_vector",
                "use_compression": True,
                "similarity_threshold": 0.3
            }
        }
        
        print(f"⏳ Sending request to {endpoint}...")
        start_time = time.time()
        
        response = requests.post(
            url,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=90
        )
        
        end_time = time.time()
        print(f"⏱️  Response time: {end_time - start_time:.2f} seconds")
        
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": f"HTTP {response.status_code}: {response.text}"}
            
    except Exception as e:
        return {"error": str(e)}

def main():
    """Main test function"""
    print("🚀 Real-time AWS MCP Troubleshooting Test")
    print("=" * 60)
    print("🎯 Testing with your actual AWS resources:")
    print("   • EC2 instances: i-0d0faa5b7e20d4bf1 (and 1 more)")
    print("   • Lambda functions: 1 function")
    print("   • S3 buckets: 5 buckets")
    print("=" * 60)
    
    # Test the dedicated troubleshooting endpoint
    test_troubleshooting_endpoint()
    
    # Compare endpoints
    compare_endpoints()
    
    print(f"\n{'='*60}")
    print("🎯 Test Complete!")
    print("=" * 60)
    print("✅ If MCP Enhanced = YES, your real-time monitoring is working!")
    print("❌ If MCP Enhanced = NO, there may be an MCP server issue")
    print("\n🔗 Backend: http://localhost:8888")

if __name__ == "__main__":
    main()
