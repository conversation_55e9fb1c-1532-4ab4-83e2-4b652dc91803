#!/usr/bin/env python3
"""
Test script for MCP AWS Integration
Validates that MCP server and integration components work correctly
"""

import asyncio
import json
import logging
import os
import sys
from datetime import datetime
from dotenv import load_dotenv

# Add current directory to path for imports
sys.path.append(os.path.dirname(__file__))

from mcp_rag_integration import MCPRAGIntegration, MCPEnhancedQueryEngine
from query import QueryEngine

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

load_dotenv()

class MCPIntegrationTester:
    """Test suite for MCP AWS integration"""
    
    def __init__(self):
        self.test_results = []
        
    async def run_all_tests(self):
        """Run all MCP integration tests"""
        logger.info("🧪 Starting MCP Integration Test Suite")
        logger.info("=" * 50)
        
        tests = [
            self.test_aws_credentials,
            self.test_mcp_server_startup,
            self.test_basic_mcp_tools,
            self.test_rag_integration,
            self.test_enhanced_query_engine,
            self.test_troubleshooting_scenarios
        ]
        
        for test in tests:
            try:
                await test()
                self.test_results.append({"test": test.__name__, "status": "PASS"})
            except Exception as e:
                logger.error(f"❌ Test {test.__name__} failed: {e}")
                self.test_results.append({"test": test.__name__, "status": "FAIL", "error": str(e)})
        
        self.print_test_summary()
    
    async def test_aws_credentials(self):
        """Test AWS credentials and permissions"""
        logger.info("🔐 Testing AWS credentials...")
        
        import boto3
        from botocore.exceptions import NoCredentialsError, ClientError
        
        try:
            # Test basic AWS access
            session = boto3.Session(
                aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
                aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
                region_name=os.getenv("AWS_REGION", "us-east-1")
            )
            
            sts = session.client('sts')
            identity = sts.get_caller_identity()
            logger.info(f"✅ AWS credentials valid for account: {identity['Account']}")
            
            # Test CloudWatch access
            cloudwatch = session.client('cloudwatch')
            cloudwatch.list_metrics(MaxRecords=1)
            logger.info("✅ CloudWatch access confirmed")
            
            # Test EC2 access
            ec2 = session.client('ec2')
            ec2.describe_instances(MaxResults=5)
            logger.info("✅ EC2 access confirmed")
            
        except NoCredentialsError:
            raise Exception("AWS credentials not found or invalid")
        except ClientError as e:
            if e.response['Error']['Code'] == 'UnauthorizedOperation':
                raise Exception(f"Insufficient AWS permissions: {e}")
            raise
    
    async def test_mcp_server_startup(self):
        """Test MCP server startup and basic functionality"""
        logger.info("🚀 Testing MCP server startup...")
        
        try:
            # Initialize query engine (needed for integration)
            query_engine = QueryEngine()
            
            # Initialize MCP integration
            mcp_integration = MCPRAGIntegration(query_engine)
            
            # Start MCP server
            success = await mcp_integration.start_mcp_server()
            if not success:
                raise Exception("Failed to start MCP server")
            
            logger.info("✅ MCP server started successfully")
            
            # Test basic server communication
            try:
                response = await mcp_integration.send_mcp_request(
                    "tools/list",
                    {}
                )
                logger.info("✅ MCP server communication working")
            except Exception as e:
                logger.warning(f"⚠️  MCP server communication test failed: {e}")
            
            # Cleanup
            await mcp_integration.stop_mcp_server()
            logger.info("✅ MCP server stopped cleanly")
            
        except Exception as e:
            raise Exception(f"MCP server test failed: {e}")
    
    async def test_basic_mcp_tools(self):
        """Test individual MCP tools"""
        logger.info("🔧 Testing MCP tools...")
        
        query_engine = QueryEngine()
        mcp_integration = MCPRAGIntegration(query_engine)
        
        try:
            await mcp_integration.start_mcp_server()
            
            # Test CloudWatch metrics tool
            try:
                response = await mcp_integration.send_mcp_request(
                    "tools/call",
                    {
                        "name": "get_cloudwatch_metrics",
                        "arguments": {
                            "namespace": "AWS/EC2",
                            "metric_name": "CPUUtilization"
                        }
                    }
                )
                logger.info("✅ CloudWatch metrics tool working")
            except Exception as e:
                logger.warning(f"⚠️  CloudWatch metrics tool test failed: {e}")
            
            # Test EC2 health tool
            try:
                response = await mcp_integration.send_mcp_request(
                    "tools/call",
                    {
                        "name": "get_ec2_instance_health",
                        "arguments": {}
                    }
                )
                logger.info("✅ EC2 health tool working")
            except Exception as e:
                logger.warning(f"⚠️  EC2 health tool test failed: {e}")
            
        finally:
            await mcp_integration.stop_mcp_server()
    
    async def test_rag_integration(self):
        """Test RAG system integration"""
        logger.info("📚 Testing RAG integration...")
        
        try:
            # Initialize query engine
            query_engine = QueryEngine()
            
            # Test basic RAG query
            result = query_engine.query_advanced(
                question="How do I troubleshoot EC2 performance issues?",
                retrieval_config={"retriever_type": "multi_vector"}
            )
            
            if result and "answer" in result:
                logger.info("✅ RAG system working")
            else:
                raise Exception("RAG query returned invalid result")
                
        except Exception as e:
            raise Exception(f"RAG integration test failed: {e}")
    
    async def test_enhanced_query_engine(self):
        """Test the enhanced query engine with MCP"""
        logger.info("🔄 Testing enhanced query engine...")
        
        try:
            # Initialize base query engine
            base_engine = QueryEngine()
            
            # Initialize enhanced engine
            enhanced_engine = MCPEnhancedQueryEngine(base_engine)
            await enhanced_engine.initialize()
            
            # Test enhanced query
            result = await enhanced_engine.query_with_mcp(
                question="My EC2 instance is running slowly, what should I check?",
                retrieval_config={}
            )
            
            if result and "answer" in result:
                logger.info("✅ Enhanced query engine working")
                if result.get("enhanced_with_mcp"):
                    logger.info("✅ MCP enhancement active")
                else:
                    logger.info("ℹ️  Query processed without MCP enhancement (normal for non-troubleshooting queries)")
            else:
                raise Exception("Enhanced query returned invalid result")
            
            await enhanced_engine.cleanup()
            
        except Exception as e:
            raise Exception(f"Enhanced query engine test failed: {e}")
    
    async def test_troubleshooting_scenarios(self):
        """Test specific troubleshooting scenarios"""
        logger.info("🔍 Testing troubleshooting scenarios...")
        
        test_scenarios = [
            "My application is experiencing high latency",
            "I'm seeing 500 errors in my logs",
            "My database connections are timing out",
            "EC2 instance CPU usage is very high"
        ]
        
        try:
            base_engine = QueryEngine()
            enhanced_engine = MCPEnhancedQueryEngine(base_engine)
            await enhanced_engine.initialize()
            
            for scenario in test_scenarios:
                try:
                    result = await enhanced_engine.query_with_mcp(
                        question=scenario,
                        retrieval_config={}
                    )
                    
                    if result and "answer" in result:
                        logger.info(f"✅ Scenario '{scenario[:30]}...' processed successfully")
                    else:
                        logger.warning(f"⚠️  Scenario '{scenario[:30]}...' returned invalid result")
                        
                except Exception as e:
                    logger.warning(f"⚠️  Scenario '{scenario[:30]}...' failed: {e}")
            
            await enhanced_engine.cleanup()
            logger.info("✅ Troubleshooting scenarios test completed")
            
        except Exception as e:
            raise Exception(f"Troubleshooting scenarios test failed: {e}")
    
    def print_test_summary(self):
        """Print test results summary"""
        logger.info("\n" + "=" * 50)
        logger.info("📊 MCP Integration Test Results")
        logger.info("=" * 50)
        
        passed = sum(1 for result in self.test_results if result["status"] == "PASS")
        failed = sum(1 for result in self.test_results if result["status"] == "FAIL")
        total = len(self.test_results)
        
        for result in self.test_results:
            status_icon = "✅" if result["status"] == "PASS" else "❌"
            logger.info(f"{status_icon} {result['test']}: {result['status']}")
            if result["status"] == "FAIL":
                logger.info(f"   Error: {result.get('error', 'Unknown error')}")
        
        logger.info(f"\nSummary: {passed}/{total} tests passed")
        
        if failed == 0:
            logger.info("🎉 All tests passed! MCP integration is ready to use.")
        else:
            logger.info(f"⚠️  {failed} test(s) failed. Please review the errors above.")
        
        return failed == 0

async def main():
    """Main test runner"""
    tester = MCPIntegrationTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎉 MCP Integration is working correctly!")
        print("You can now use the enhanced troubleshooting features.")
    else:
        print("\n❌ Some tests failed. Please review the output above.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
